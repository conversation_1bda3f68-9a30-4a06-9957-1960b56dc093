package com.hvisions.productBoard.service;

import lombok.var;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class UnitTest {

    @Resource
    private BiAssemblyService biAssemblyService;

    @Resource
    private BiOfficeService biOfficeService;

    @Resource
    private PmAssemblyService pmAssemblyService;

    @Test
    public void test01() {
        long start = System.currentTimeMillis();
        var resp = biOfficeService.getOverview();
        long end = System.currentTimeMillis();
        System.out.println("耗时：" + (end - start));
        System.out.println(resp);
    }


}

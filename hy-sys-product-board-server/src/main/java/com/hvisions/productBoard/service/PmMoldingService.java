package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.productBoard.entity.HyPmMoldingBlankWorkOrder;
import com.hvisions.productBoard.entity.HyPmMoldingWorkOrder;
import com.hvisions.productBoard.enums.MoldingTaskStateEnum;
import com.hvisions.productBoard.enums.MoldingTaskTypeEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.HyPmMoldingBlankWorkOrderMapper;
import com.hvisions.productBoard.mapper.HyPmMoldingWorkOrderMapper;
import com.hvisions.productBoard.resp.PmMoldingCompleteRateResp;
import com.hvisions.productBoard.resp.PmMoldingDataOverviewResp;
import com.hvisions.productBoard.resp.PmMoldingStepOutputResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PmMoldingService {

    @Resource
    private HyPmMoldingWorkOrderMapper hyPmMoldingWorkOrderMapper;
    @Resource
    private HyPmMoldingBlankWorkOrderMapper hyPmMoldingBlankWorkOrderMapper;

    public PmMoldingDataOverviewResp getDataOverview() {

        // 未完成的订单
        List<HyPmMoldingWorkOrder> order = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .eq(HyPmMoldingWorkOrder::getState, MoldingTaskStateEnum.RUNNING.getValue())
        );

        // 计划完成日期一个月内的订单
        List<HyPmMoldingWorkOrder> nearOrder = order.stream().filter(
                o -> {
                    if (o.getPlanEndTime() == null) {
                        return false;
                    }
                    LocalDate planEndDate = o.getPlanEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    return planEndDate.isAfter(LocalDate.now()) && planEndDate.isBefore(LocalDate.now().plusMonths(1));
                }
        ).collect(Collectors.toList());

        // 计划完成日期超期的订单
        List<HyPmMoldingWorkOrder> errorOrder = order.stream().filter(
                o -> {
                    if (o.getPlanEndTime() == null) {
                        return false;
                    }
                    LocalDate planEndDate = o.getPlanEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    return planEndDate.isBefore(LocalDate.now());
                }
        ).collect(Collectors.toList());

        return PmMoldingDataOverviewResp.builder()
                .orderCount(order.size())
                .orderQuantity(order.stream().map(this::getUnfinishedQuantity).reduce(BigDecimal.ZERO, BigDecimal::add))
                .nearOrderCount(nearOrder.size())
                .nearOrderQuantity(nearOrder.stream().map(this::getUnfinishedQuantity).reduce(BigDecimal.ZERO, BigDecimal::add))
                .errorOrderCount(errorOrder.size())
                .errorOrderQuantity(errorOrder.stream().map(this::getUnfinishedQuantity).reduce(BigDecimal.ZERO, BigDecimal::add))
                .build();
    }

    // 获取订单中未完成的数量
    private BigDecimal getUnfinishedQuantity(HyPmMoldingWorkOrder order) {
        BigDecimal planQuantity = order.getPlanQuantity() == null ? BigDecimal.ZERO : order.getPlanQuantity();
        BigDecimal finishedQuantity = order.getFinishedQuantity() == null ? BigDecimal.ZERO : order.getFinishedQuantity();
        return planQuantity.subtract(finishedQuantity);
    }


    public List<PmMoldingCompleteRateResp> getCompleteRate(TimePeriodEnum periodEnum) {
        List<PmMoldingCompleteRateResp> resultList = new ArrayList<>();
        LocalDate today = LocalDate.now();

        switch (periodEnum) {
            case YEAR:
                int currentYear = today.getYear();
                for (int month = 1; month <= 12; month++) {
                    LocalDate startDate = LocalDate.of(currentYear, month, 1);
                    LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
                    String dateLabel = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                    resultList.add(calculateRateForPeriod(startDate, endDate, dateLabel, periodEnum.getName()));
                }
                break;
            case MONTH:
                LocalDate monthStart = today.withDayOfMonth(1);
                LocalDate monthEnd = today.withDayOfMonth(today.lengthOfMonth());
                
                for (int week = 1; week <= 4; week++) {
                    LocalDate weekStart = monthStart.plusWeeks(week - 1);
                    LocalDate weekEnd = weekStart.plusDays(6);
                    
                    if (weekStart.isBefore(monthStart)) {
                        weekStart = monthStart;
                    }
                    if (weekEnd.isAfter(monthEnd)) {
                        weekEnd = monthEnd;
                    }
                    
                    String dateLabel = "第" + week + "周";
                    resultList.add(calculateRateForPeriod(weekStart, weekEnd, dateLabel, periodEnum.getName()));
                }
                break;
            case WEEK:
                LocalDate weekStart = today.with(WeekFields.of(Locale.getDefault()).dayOfWeek(), 1);
                for (int day = 0; day < 7; day++) {
                    LocalDate date = weekStart.plusDays(day);
                    String dateStr = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
                    resultList.add(calculateRateForPeriod(date, date, dateStr, periodEnum.getName()));
                }
                break;
            default:
                throw new IllegalArgumentException("不支持此时间维度: " + periodEnum);
        }

        return resultList;
    }

    private PmMoldingCompleteRateResp calculateRateForPeriod(LocalDate startDate, LocalDate endDate, String dateStr, String periodName) {
        List<HyPmMoldingWorkOrder> orders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .isNotNull(HyPmMoldingWorkOrder::getActualEndTime)
                        .between(HyPmMoldingWorkOrder::getActualEndTime, startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay())
        );

        BigDecimal totalPlanQuantity = orders.stream()
                .map(HyPmMoldingWorkOrder::getPlanQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalFinishedQuantity = orders.stream()
                .map(HyPmMoldingWorkOrder::getFinishedQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal rate = BigDecimal.ZERO;
        if (totalPlanQuantity.compareTo(BigDecimal.ZERO) > 0) {
            rate = totalFinishedQuantity.divide(totalPlanQuantity, 4, RoundingMode.HALF_UP);
        }

        return PmMoldingCompleteRateResp.builder()
                .output(totalFinishedQuantity)
                .rate(rate)
                .dateStr(dateStr)
                .period(periodName)
                .build();
    }

    public List<PmMoldingStepOutputResp> getStepOutput(TimePeriodEnum periodEnum) {
        LocalDate today = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;

        switch (periodEnum) {
            case YEAR:
                startDate = today.withDayOfYear(1);
                endDate = today.withDayOfYear(today.lengthOfYear());
                break;
            case MONTH:
                startDate = today.withDayOfMonth(1);
                endDate = today.withDayOfMonth(today.lengthOfMonth());
                break;
            case WEEK:
                startDate = today.with(DayOfWeek.MONDAY);
                endDate = today.with(DayOfWeek.SUNDAY);
                break;
            default:
                throw new IllegalArgumentException("不支持此时间维度：" + periodEnum);
        }

        QueryWrapper<HyPmMoldingBlankWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("type, sum(actual_quantity) as actual_quantity")
                .between("actual_end_date_time", startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay())
                .isNotNull("actual_quantity")
                .groupBy("type");

        List<HyPmMoldingBlankWorkOrder> result = hyPmMoldingBlankWorkOrderMapper.selectList(queryWrapper);

        Map<Integer, String> taskTypeMap = Arrays.stream(MoldingTaskTypeEnum.values())
                .collect(Collectors.toMap(MoldingTaskTypeEnum::getValue, MoldingTaskTypeEnum::getName));

        return result.stream()
                .map(item -> new PmMoldingStepOutputResp(
                        item.getActualQuantity(),
                        taskTypeMap.get(item.getType())
                ))
                .collect(Collectors.toList());
    }
}

package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.productBoard.entity.HyPmMoldingBlankWorkOrder;
import com.hvisions.productBoard.entity.HyPmMoldingWorkOrder;
import com.hvisions.productBoard.enums.MoldingTestTaskStateEnum;
import com.hvisions.productBoard.enums.MoldingWorkOrderStateEnum;
import com.hvisions.productBoard.mapper.HyPmMoldingBlankWorkOrderMapper;
import com.hvisions.productBoard.mapper.HyPmMoldingWorkOrderMapper;
import com.hvisions.productBoard.req.CommonPageReq;
import com.hvisions.productBoard.resp.IndexMoldingDataOverviewResp;
import com.hvisions.productBoard.resp.IndexMoldingKeyOrderResp;
import com.hvisions.productBoard.resp.IndexMoldingTestOrderResp;
import com.hvisions.productBoard.util.PageHelperAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexMoldingService {

    @Resource
    private HyPmMoldingWorkOrderMapper hyPmMoldingWorkOrderMapper;
    @Resource
    private HyPmMoldingBlankWorkOrderMapper hyPmMoldingBlankWorkOrderMapper;

    public IndexMoldingDataOverviewResp getDataOverview() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

        List<HyPmMoldingWorkOrder> ongoingOrders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .eq(HyPmMoldingWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCING.getValue())
        );
        Integer ongoingOrder = ongoingOrders.size();

        List<HyPmMoldingWorkOrder> newOrders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .between(HyPmMoldingWorkOrder::getCreateTime, todayStart, todayEnd)
        );
        Integer newOrder = newOrders.size();

        LocalDateTime nearDate = today.plusMonths(1).atTime(LocalTime.MAX);
        List<HyPmMoldingWorkOrder> nearOrders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .between(HyPmMoldingWorkOrder::getPlanEndTime, todayStart, nearDate)
        );
        Integer nearOrder = nearOrders.size();

        List<HyPmMoldingWorkOrder> todayFinishedOrders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .eq(HyPmMoldingWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCED_FINISH.getValue())
                        .between(HyPmMoldingWorkOrder::getActualEndTime, todayStart, todayEnd)
        );
        Integer todayFinishOrder = todayFinishedOrders.size();

        BigDecimal todayOutput = todayFinishedOrders.stream()
                .map(order -> order.getFinishedQuantity() != null ? order.getFinishedQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return IndexMoldingDataOverviewResp.builder()
                .ongoingOrder(ongoingOrder)
                .newOrder(newOrder)
                .nearOrder(nearOrder)
                .todayFinishOrder(todayFinishOrder)
                .todayOutput(todayOutput)
                .build();
    }

    public Page<IndexMoldingKeyOrderResp> getKeyOrder(Integer current, Integer size) {
        CommonPageReq pageReq = PageHelperAdapter.createPageReq(current, size);

        org.springframework.data.domain.Page<HyPmMoldingBlankWorkOrder> page = PageHelperUtil.getPage(() -> {
            QueryWrapper<HyPmMoldingBlankWorkOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(HyPmMoldingBlankWorkOrder::getTrialState, MoldingTestTaskStateEnum.PRODUCED.getValue())
                    .ne(HyPmMoldingBlankWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCED_FINISH.getValue())
                    .ne(HyPmMoldingBlankWorkOrder::getState, MoldingWorkOrderStateEnum.SUBMIT.getValue())
                    .orderByAsc(HyPmMoldingBlankWorkOrder::getPlanDateTime);
            return hyPmMoldingBlankWorkOrderMapper.selectList(queryWrapper);
        }, pageReq);

        List<IndexMoldingKeyOrderResp> records = page.getContent().stream().map(order -> {
            double percentage = 0.0;
            if (order.getPlanQuantity() != null && order.getPlanQuantity().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal actualQty = order.getActualQuantity() != null ? order.getActualQuantity() : BigDecimal.ZERO;
                percentage = actualQty.divide(order.getPlanQuantity(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).doubleValue();
            }

            return IndexMoldingKeyOrderResp.builder()
                    .orderCode(order.getWorkOrderCode())
                    .productName(order.getProductName())
                    .productEigenvalue(order.getProductEigenvalue())
                    .orderQuantity(order.getPlanQuantity() != null ? order.getPlanQuantity().intValue() : 0)
                    .batchNum(order.getProductBatchNumber())
                    .percentage(percentage)
                    .productionQuantity(order.getActualQuantity() != null ? order.getActualQuantity() : BigDecimal.ZERO)
                    .build();
        }).collect(Collectors.toList());

        Page<IndexMoldingKeyOrderResp> resultPage = new Page<>(current, size);
        resultPage.setTotal(page.getTotalElements());
        resultPage.setPages((long) page.getTotalPages());
        resultPage.setRecords(records);
        return resultPage;
    }

    public Page<IndexMoldingTestOrderResp> getTestOrder(Integer current, Integer size) {
        CommonPageReq pageReq = PageHelperAdapter.createPageReq(current, size);

        org.springframework.data.domain.Page<HyPmMoldingBlankWorkOrder> page = PageHelperUtil.getPage(() -> {
            QueryWrapper<HyPmMoldingBlankWorkOrder> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .in(HyPmMoldingBlankWorkOrder::getTrialState,
                            MoldingTestTaskStateEnum.READY.getValue(),
                            MoldingTestTaskStateEnum.PRODUCING.getValue())
                    .ne(HyPmMoldingBlankWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCED_FINISH.getValue())
                    .ne(HyPmMoldingBlankWorkOrder::getState, MoldingWorkOrderStateEnum.SUBMIT.getValue())
                    .orderByAsc(HyPmMoldingBlankWorkOrder::getPlanDateTime);
            return hyPmMoldingBlankWorkOrderMapper.selectList(queryWrapper);
        }, pageReq);

        List<IndexMoldingTestOrderResp> records = page.getContent().stream().map(order -> {
            double percentage = 0.0;
            if (order.getPlanQuantity() != null && order.getPlanQuantity().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal actualQty = order.getActualQuantity() != null ? order.getActualQuantity() : BigDecimal.ZERO;
                percentage = actualQty.divide(order.getPlanQuantity(), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100")).doubleValue();
            }

            return IndexMoldingTestOrderResp.builder()
                    .orderCode(order.getWorkOrderCode())
                    .productName(order.getProductName())
                    .productEigenvalue(order.getProductEigenvalue())
                    .orderQuantity(order.getPlanQuantity() != null ? order.getPlanQuantity().intValue() : 0)
                    .batchNum(order.getProductBatchNumber())
                    .percentage(percentage)
                    .productionQuantity(order.getActualQuantity() != null ? order.getActualQuantity() : BigDecimal.ZERO)
                    .build();
        }).collect(Collectors.toList());

        Page<IndexMoldingTestOrderResp> resultPage = new Page<>(current, size);
        resultPage.setTotal(page.getTotalElements());
        resultPage.setPages((long) page.getTotalPages());
        resultPage.setRecords(records);
        return resultPage;
    }

    public String getKeyMaterials() {
        return null;
    }

    public String getNotice() {
        return null;
    }
}

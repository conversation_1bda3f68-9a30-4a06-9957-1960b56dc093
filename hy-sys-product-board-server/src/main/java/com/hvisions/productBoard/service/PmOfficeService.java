package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.productBoard.entity.PmAssemblyWorkOrder;
import com.hvisions.productBoard.entity.SaSaleOrder;
import com.hvisions.productBoard.mapper.PmAssemblyWorkOrderMapper;
import com.hvisions.productBoard.mapper.SaSaleOrderMapper;
import com.hvisions.productBoard.req.PmOfficeAssemblyPlanQuantityAndPercentReq;
import com.hvisions.productBoard.resp.PmOfficeAssemblyPlanQuantityAndPercentResp;
import com.hvisions.productBoard.resp.PmOfficeOverviewResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产首页 五部办公室首页
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class PmOfficeService {

    private final SaSaleOrderMapper saSaleOrderMapper;

    private final PmAssemblyWorkOrderMapper pmAssemblyWorkOrderMapper;

    /**
     * 新增合同数：当日新增备货需求单数
     * 产品入库根数：当日完工入库单完成根数
     * 瓶颈订单数【需要先完成MES改动】：当日 金属件领料不足、硫化未完成
     * 异常订单：异常备货需求单（超期）
     * 临期订单数：临期备货需求单
     */
    public PmOfficeOverviewResp getOverview() {
        PmOfficeOverviewResp resp = new PmOfficeOverviewResp();

        LocalDate today = LocalDate.now();

        // 新增合同数
        Long newSaleOrderCount = 0L;
        try {
            Wrapper<SaSaleOrder> wrapper = Wrappers.lambdaQuery(SaSaleOrder.class)
                    .ge(SaSaleOrder::getCreateTime, today.atTime(0, 0, 0));

            newSaleOrderCount = saSaleOrderMapper.selectCount(wrapper);
        } catch (Exception ignore) {
        } finally {
            resp.setNewSaleOrderCount(newSaleOrderCount);
        }

        return resp;
    }

    /**
     * 总装计划达成率 （与五部办公室看板 - 总装班组 计划完成率一致）
     */
    public PmOfficeAssemblyPlanQuantityAndPercentResp getAssemblyPlanQuantityAndPercent(PmOfficeAssemblyPlanQuantityAndPercentReq req) {
        String mode = Optional.ofNullable(req.getMode())
                .orElseThrow(() -> new BaseKnownException("年月周字段为空"));

        PmOfficeAssemblyPlanQuantityAndPercentResp resp = new PmOfficeAssemblyPlanQuantityAndPercentResp();

        List<String> xAxisData;
        Map<String, BigDecimal> quantityMap;
        Map<String, BigDecimal> percentMap;

        LocalDate today = LocalDate.now();

        // 年
        if (Objects.equals("YEAR", mode)) {
            LocalDate start = today.with(TemporalAdjusters.firstDayOfYear());
            LocalDate end = today.with(TemporalAdjusters.lastDayOfYear());

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

            List<LocalDate> dates = new ArrayList<>();

            int monthValue = today.getMonthValue();
            for (int i = 0; i < monthValue; i++) {
                dates.add(start.plusMonths(i));
            }

            xAxisData = dates.stream().map(formatter::format).collect(Collectors.toList());

            List<PmAssemblyWorkOrder> list = pmAssemblyWorkOrderMapper.selectListByPlanEndTimeBetween(
                    start.atTime(0, 0, 0),
                    end.atTime(23, 59, 59)
            );
            if (CollectionUtils.isEmpty(list)) {
                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
                percentMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
            } else {
                Map<String, List<PmAssemblyWorkOrder>> map = list.stream()
                        .filter(i -> Objects.nonNull(i.getPlanEndTime()))
                        .collect(Collectors.groupingBy(i -> formatter.format(i.getPlanEndTime())));

                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getProductQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
                percentMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            BigDecimal total1 = map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getProductQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            BigDecimal total2 = map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getPlanQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            if (total1.compareTo(BigDecimal.ZERO) < 0 || total2.compareTo(BigDecimal.ZERO) <= 0) {
                                return BigDecimal.ZERO;
                            }

                            return new BigDecimal("100").multiply(total1).divide(total2, 2, RoundingMode.HALF_UP);
                        }));
            }
        }
        // 月
        else if (Objects.equals("MONTH", mode)) {
            LocalDate start = today.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate end = today.with(TemporalAdjusters.lastDayOfMonth());

            List<LocalDate> dates = new ArrayList<>();

            int endDayOfMonth = end.getDayOfMonth();
            for (int i = 0; i < endDayOfMonth; i++) {
                dates.add(start.plusDays(i));
            }

            int maxWeekOfMonth = dates.stream()
                    .map(date -> date.get(WeekFields.ISO.weekOfMonth()))
                    .max(Integer::compareTo)
                    .orElse(4);

            List<Integer> weekNums = new ArrayList<>();
            for (int i = 1; i <= maxWeekOfMonth; i++) {
                weekNums.add(i);
            }

            xAxisData = weekNums.stream()
                    .map(weekNum -> String.format("第%s周", weekNum))
                    .collect(Collectors.toList());

            List<PmAssemblyWorkOrder> list = pmAssemblyWorkOrderMapper.selectListByPlanEndTimeBetween(
                    start.atTime(0, 0, 0),
                    end.atTime(23, 59, 59)
            );
            if (CollectionUtils.isEmpty(list)) {
                quantityMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> BigDecimal.ZERO));
                percentMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> BigDecimal.ZERO));
            } else {
                Map<Integer, List<PmAssemblyWorkOrder>> map = list.stream()
                        .filter(item -> Objects.nonNull(item.getPlanEndTime()))
                        .collect(Collectors.groupingBy(item -> item.getPlanEndTime().get(WeekFields.ISO.weekOfMonth())));

                quantityMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getProductQuantity)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
                percentMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            BigDecimal total1 = map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getProductQuantity)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            BigDecimal total2 = map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getPlanQuantity)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            if (total1.compareTo(BigDecimal.ZERO) < 0 || total2.compareTo(BigDecimal.ZERO) <= 0) {
                                return BigDecimal.ZERO;
                            }

                            return new BigDecimal("100").multiply(total1).divide(total2, 2, RoundingMode.HALF_UP);
                        }));
            }
        }
        // 周
        else if (Objects.equals("WEEK", mode)) {
            LocalDate start = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            LocalDate end = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            List<LocalDate> dates = new ArrayList<>();

            while (!start.isAfter(end)) {
                dates.add(start);

                start = start.plusDays(1);
            }

            xAxisData = dates.stream().map(formatter::format).collect(Collectors.toList());

            List<PmAssemblyWorkOrder> list = pmAssemblyWorkOrderMapper.selectListByPlanEndTimeBetween(
                    start.atTime(0, 0, 0),
                    end.atTime(23, 59, 59)
            );
            if (CollectionUtils.isEmpty(list)) {
                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
                percentMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
            } else {
                Map<String, List<PmAssemblyWorkOrder>> map = list.stream()
                        .filter(i -> Objects.nonNull(i.getPlanEndTime()))
                        .collect(Collectors.groupingBy(i -> formatter.format(i.getPlanEndTime())));

                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getProductQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
                percentMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            BigDecimal total1 = map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getProductQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            BigDecimal total2 = map.get(i).stream()
                                    .map(PmAssemblyWorkOrder::getPlanQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            if (total1.compareTo(BigDecimal.ZERO) < 0 || total2.compareTo(BigDecimal.ZERO) <= 0) {
                                return BigDecimal.ZERO;
                            }

                            return new BigDecimal("100").multiply(total1).divide(total2, 2, RoundingMode.HALF_UP);
                        }));
            }
        }
        // 其他
        else {
            throw new BaseKnownException("年月周字段错误");
        }

        resp.setXAxisData(xAxisData);
        resp.setQuantityMap(quantityMap);
        resp.setPercentMap(percentMap);

        return resp;
    }

    // ? 销售临期订单列表
    // ? 查询参数：默认展示临期合同，输入合同号可以查询非临期合同

    // ? xxx 合同未完成任务（备货需求单明细）
    // ? 原料准备：已领金属件数量 / 总需求金属件数量
    // ? 生产执行：已完成根数 / 订单总数量（根）
    // ? 外厂检验：已完成检验数 / 订单总数量（根）
    // ? 产品检验：软管组件检验单完成数量 / 订单总数量（根）
    // ? 军方检验：软管组件检验单军检完成数量 / 订单总数量（根）【需要MES先完成调整】
    // ? 完工入库：完工入库数量 / 订单总数量（根）
}

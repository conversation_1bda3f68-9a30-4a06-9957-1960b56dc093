package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.productBoard.dto.PmAssemblyWorkSubOrderDTO;
import com.hvisions.productBoard.entity.*;
import com.hvisions.productBoard.mapper.*;
import com.hvisions.productBoard.req.PmAssemblyKeyOrderReq;
import com.hvisions.productBoard.req.PmAssemblyStepOutputReq;
import com.hvisions.productBoard.req.PmAssemblyWorkSubOrderPageQueryReq;
import com.hvisions.productBoard.resp.PmAssemblyKeyOrderResp;
import com.hvisions.productBoard.resp.PmAssemblyOverviewResp;
import com.hvisions.productBoard.resp.PmAssemblyStepOutputResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 生产首页 五部总装首页
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class PmAssemblyService {

    private final BmMaterialMapper bmMaterialMapper;

    private final BmBomMapper bmBomMapper;
    private final BmBomItemMapper bmBomItemMapper;

    private final WmDeliverOrderLineMapper wmDeliverOrderLineMapper;

    private final PmAssemblyWorkOrderMapper pmAssemblyWorkOrderMapper;

    private final PmAssemblyWorkSubOrderOperationOutputMapper pmAssemblyWorkSubOrderOperationOutputMapper;

    private final QmHoseAssemblyOrderMapper qmHoseAssemblyOrderMapper;

    // ! 首页 -----------------------------------------------------------------------------------------------------------

    /**
     * 数据概览
     */
    public PmAssemblyOverviewResp getOverview() {
        LocalDate today = LocalDate.now();

        // ExecutorService executorService = Executors.newFixedThreadPool(4);

        int cores = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                (int)(cores * 1.5) + 1,
                cores * 2,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // ! 进行中的任务数量
        CompletableFuture<Long> futureA = CompletableFuture.supplyAsync(() -> {
                    Wrapper<PmAssemblyWorkOrder> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                            .eq(PmAssemblyWorkOrder::getState, 10);

                    return pmAssemblyWorkOrderMapper.selectCount(wrapper);
                }, executorService)
                .exceptionally(e -> 0L);

        // ! 新增任务数量
        CompletableFuture<Long> futureB = CompletableFuture.supplyAsync(() -> {
                    Wrapper<PmAssemblyWorkOrder> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                            .ge(PmAssemblyWorkOrder::getCreateTime, today.atTime(0, 0, 0))
                            .le(PmAssemblyWorkOrder::getCreateTime, today.atTime(23, 59, 59));

                    return pmAssemblyWorkOrderMapper.selectCount(wrapper);
                }, executorService)
                .exceptionally(e -> 0L);

        // TODO 瓶颈订单数量
        CompletableFuture<Long> futureC = CompletableFuture.supplyAsync(() -> {
                    return 0L;
                }, executorService)
                .exceptionally(e -> 0L);

        // TODO 缺少零件数量
        CompletableFuture<BigDecimal> futureD = CompletableFuture.supplyAsync(() -> {
                    return BigDecimal.ZERO;
                }, executorService)
                .exceptionally(e -> BigDecimal.ZERO);

        // TODO 可用零件数量
        CompletableFuture<BigDecimal> futureE = CompletableFuture.supplyAsync(() -> {
                    return BigDecimal.ZERO;
                }, executorService)
                .exceptionally(e -> BigDecimal.ZERO);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futureA, futureB);

        executorService.shutdown();

        return allOf
                .thenApply(v -> {
                    PmAssemblyOverviewResp resp = new PmAssemblyOverviewResp();

                    resp.setIngOrderCount(futureA.join());
                    resp.setNewOrderCount(futureB.join());
                    resp.setBottleneckOrderCount(futureC.join());

                    resp.setLackPartsQuantity(futureD.join());
                    resp.setAvailablePartsQuantity(futureE.join());

                    return resp;
                })
                .join();
    }

    /**
     * 工位产出
     */
    public PmAssemblyStepOutputResp getStepOutput(PmAssemblyStepOutputReq req) {
        String mode = Optional.ofNullable(req.getMode())
                .orElseThrow(() -> new BaseKnownException("年月周字段为空"));

        PmAssemblyStepOutputResp resp = new PmAssemblyStepOutputResp();
        resp.setXAxisData(Lists.newArrayList("落料", "清洁&护套", "螺母装配", "接头装配(插入式)", "接头装配(分离式)", "登记", "扣压", "复验", "标牌制作"));

        LocalDate today = LocalDate.now();

        LocalDate start;
        LocalDate end;
        if (Objects.equals("YEAR", mode)) {
            start = today.with(TemporalAdjusters.firstDayOfYear());
            end = today.with(TemporalAdjusters.lastDayOfYear());
        } else if (Objects.equals("MONTH", mode)) {
            start = today.with(TemporalAdjusters.firstDayOfMonth());
            end = today.with(TemporalAdjusters.lastDayOfMonth());
        } else if (Objects.equals("WEEK", mode)) {
            start = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            end = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        } else {
            throw new BaseKnownException("年月周字段错误");
        }

        List<PmAssemblyWorkSubOrderOperationOutput> list = pmAssemblyWorkSubOrderOperationOutputMapper.selectListByCreateTimeBetween(
                start.atTime(0, 0, 0),
                end.atTime(23, 59, 59)
        );

        resp.setQuantityMap(getStepOutputQuantity(list));

        return resp;
    }

    private Map<String, BigDecimal> getStepOutputQuantity(List<PmAssemblyWorkSubOrderOperationOutput> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new HashMap<>();
        }

        Map<String, BigDecimal> map = new HashMap<>();

        Map<String, List<PmAssemblyWorkSubOrderOperationOutput>> operationNameMap = list.stream()
                .collect(Collectors.groupingBy(PmAssemblyWorkSubOrderOperationOutput::getOperationName));

        // ! 落料
        BigDecimal llQuantity = Optional.ofNullable(operationNameMap.get("落料"))
                .filter(CollectionUtils::isNotEmpty)
                .map(outputs -> outputs.stream()
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .orElse(BigDecimal.ZERO);
        map.put("落料", llQuantity);

        // ! 清洁&护套
        BigDecimal qjQuantity = Optional.ofNullable(operationNameMap.get("清洁"))
                .filter(CollectionUtils::isNotEmpty)
                .map(outputs -> outputs.stream()
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .orElse(BigDecimal.ZERO);
        BigDecimal fhtQuantity = Optional.ofNullable(operationNameMap.get("防护套"))
                .filter(CollectionUtils::isNotEmpty)
                .map(outputs -> outputs.stream()
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .orElse(BigDecimal.ZERO);
        map.put("清洁&护套", qjQuantity.add(fhtQuantity));

        // ! 接头装配
        BigDecimal[] quantityArr1 = new BigDecimal[]{BigDecimal.ZERO};
        BigDecimal[] quantityArr2 = new BigDecimal[]{BigDecimal.ZERO};

        List<PmAssemblyWorkSubOrderOperationOutput> list1 = operationNameMap.get("接头装配");
        if (CollectionUtils.isNotEmpty(list1)) {
            List<String> orderCodes = list1.stream()
                    .map(PmAssemblyWorkSubOrderOperationOutput::getOrderCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderCodes)) {
                List<PmAssemblyWorkOrder> workOrders = pmAssemblyWorkOrderMapper.selectList(
                        Wrappers.lambdaQuery(PmAssemblyWorkOrder.class).in(PmAssemblyWorkOrder::getCode, orderCodes)
                );

                if (CollectionUtils.isNotEmpty(workOrders)) {
                    list1.forEach(output -> {
                        workOrders.stream()
                                .filter(w -> Objects.equals(w.getCode(), output.getOrderCode()))
                                .findFirst()
                                .ifPresent(workOrder -> {
                                    BigDecimal count = Optional.ofNullable(output.getCount()).orElse(BigDecimal.ZERO);

                                    if (Objects.equals("插入式", workOrder.getProductionMode())) {
                                        quantityArr1[0] = quantityArr1[0].add(count);
                                    }
                                    if (Objects.equals("分离式", workOrder.getProductionMode())) {
                                        quantityArr2[0] = quantityArr2[0].add(count);
                                    }
                                });
                    });
                }
            }
        }
        map.put("接头装配(插入式)", quantityArr1[0]);
        map.put("接头装配(分离式)", quantityArr2[0]);

        // ! 螺母装配
        BigDecimal lmQuantity = Optional.ofNullable(operationNameMap.get("螺母装配"))
                .filter(CollectionUtils::isNotEmpty)
                .map(outputs -> outputs.stream()
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .orElse(BigDecimal.ZERO);
        map.put("螺母装配", lmQuantity);

        // ! 登记
        BigDecimal djQuantity = Optional.ofNullable(operationNameMap.get("登记"))
                .filter(CollectionUtils::isNotEmpty)
                .map(outputs -> outputs.stream()
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .orElse(BigDecimal.ZERO);
        map.put("登记", djQuantity);

        // ! 扣压
        BigDecimal kyQuantity = Optional.ofNullable(operationNameMap.get("扣压关键工序"))
                .filter(CollectionUtils::isNotEmpty)
                .map(outputs -> outputs.stream()
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .orElse(BigDecimal.ZERO);
        map.put("扣压", kyQuantity);

        // ! 复验
        BigDecimal fyQuantity = Optional.ofNullable(operationNameMap.get("软管组件复验"))
                .filter(CollectionUtils::isNotEmpty)
                .map(outputs -> outputs.stream()
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .orElse(BigDecimal.ZERO);
        map.put("复验", fyQuantity);

        // ! 标牌制作
        BigDecimal bpQuantity = Optional.ofNullable(operationNameMap.get("标牌制作"))
                .filter(CollectionUtils::isNotEmpty)
                .map(outputs -> outputs.stream()
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .orElse(BigDecimal.ZERO);
        map.put("标牌制作", bpQuantity);

        return map;
    }

    /**
     * 关键订单
     */
    public Page<PmAssemblyKeyOrderResp> getKeyOrders(PmAssemblyKeyOrderReq req) {
        Page<PmAssemblyWorkOrder> page = PageHelperUtil.getPage(pmAssemblyWorkOrderMapper::selectListByPmAssemblyKeyOrderReq, req);

        long totalElements = page.getTotalElements();
        Pageable pageable = page.getPageable();
        List<PmAssemblyWorkOrder> list = page.getContent();

        if (CollectionUtils.isEmpty(list)) {
            return new PageImpl<>(new ArrayList<>());
        }

        // ExecutorService executorService = Executors.newFixedThreadPool(10);
        int cores = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                (int)(cores * 1.5) + 1,
                cores * 2,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        List<CompletableFuture<PmAssemblyKeyOrderResp>> futures = list.stream()
                .map(order -> CompletableFuture.supplyAsync(() -> {
                    PmAssemblyKeyOrderResp resp = DtoMapper.convert(order, PmAssemblyKeyOrderResp.class);

                    resp.setRawMaterialPercent(BigDecimal.ZERO);
                    resp.setProductionPercent(BigDecimal.ZERO);

                    // 原料准备
                    BigDecimal rawMaterialPrepareQuantity = BigDecimal.ZERO;
                    BigDecimal rawMaterialTotalQuantity = BigDecimal.ZERO;

                    try {
                        String deliverOrderCode = Optional.ofNullable(order.getDeliverOrderCode())
                                .orElseThrow(() -> new BaseKnownException("领料单号为空"));

                        List<WmDeliverOrderLine> deliverOrderLines = wmDeliverOrderLineMapper.selectList(Wrappers.lambdaQuery(WmDeliverOrderLine.class)
                                .eq(WmDeliverOrderLine::getDeliverOrderCode, deliverOrderCode)
                        );
                        if (CollectionUtils.isEmpty(deliverOrderLines)) {
                            throw new BaseKnownException("领料单明细为空");
                        }

                        rawMaterialPrepareQuantity = deliverOrderLines.stream()
                                .filter(line -> Objects.equals("JSJ", line.getMaterialGroup()))
                                .map(WmDeliverOrderLine::getTargetQuantity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    } catch (Exception ignore) {
                    }

                    try {
                        BigDecimal planQuantity = Optional.ofNullable(order.getPlanQuantity())
                                .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                                .orElseThrow(() -> new BaseKnownException("生产任务单计划数量为空"));

                        Integer bomId = Optional.ofNullable(order.getBomId())
                                .orElseThrow(() -> new BaseKnownException("BOM ID 为空"));

                        BmBom bom = bmBomMapper.selectOne(Wrappers.lambdaQuery(BmBom.class).eq(BmBom::getId, bomId));
                        if (Objects.isNull(bom)) {
                            throw new BaseKnownException("BOM 为不存在");
                        }

                        BigDecimal bomQuantity = Optional.ofNullable(bom.getBomCount())
                                .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                                .orElseThrow(() -> new BaseKnownException("BOM 数量为空"));

                        List<BmBomItem> bomItems = bmBomItemMapper.selectList(Wrappers.lambdaQuery(BmBomItem.class).eq(BmBomItem::getBomId, bomId));
                        if (CollectionUtils.isEmpty(bomItems)) {
                            throw new BaseKnownException("BOM 子项为不存在");
                        }

                        Set<Integer> materialIds = bomItems.stream()
                                .map(BmBomItem::getMaterialsId)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet());
                        if (CollectionUtils.isEmpty(materialIds)) {
                            throw new BaseKnownException("BOM 子项对应的物料ID不存在");
                        }

                        List<BmMaterial> materials = bmMaterialMapper.selectList(Wrappers.lambdaQuery(BmMaterial.class).in(BmMaterial::getId, materialIds));
                        if (CollectionUtils.isEmpty(materials)) {
                            throw new BaseKnownException("BOM 子项对应的物料不存在");
                        }

                        List<BmBomItem> jsjBomItems = bomItems.stream()
                                .filter(bomItem -> {
                                    boolean flag;

                                    try {
                                        BmMaterial material = materials.stream()
                                                .filter(ma -> Objects.equals(ma.getId(), bomItem.getMaterialsId()))
                                                .findFirst()
                                                .orElseThrow(() -> new BaseKnownException("BOM 子项对应的物料不存在"));

                                        flag = Objects.equals("JSJ", material.getMaterialGroupCode());
                                    } catch (Exception ignore) {
                                        flag = false;
                                    }

                                    return flag;
                                })
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(jsjBomItems)) {
                            throw new BaseKnownException("BOM子项中没有金属件");
                        }

                        BigDecimal bomJsjQuantity = jsjBomItems.stream()
                                .map(BmBomItem::getBomItemCount)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        rawMaterialTotalQuantity = planQuantity.multiply(bomJsjQuantity).divide(bomQuantity, 3, RoundingMode.HALF_UP);
                    } catch (Exception ignore) {
                    }

                    if (rawMaterialPrepareQuantity.compareTo(BigDecimal.ZERO) > 0 && rawMaterialTotalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        resp.setRawMaterialPercent(new BigDecimal("100").multiply(rawMaterialPrepareQuantity).divide(rawMaterialTotalQuantity, 2, RoundingMode.HALF_UP));
                    }

                    // 生产执行
                    BigDecimal submitQuantity = BigDecimal.ZERO;
                    BigDecimal planQuantity = BigDecimal.ZERO;

                    try {
                        planQuantity = Optional.ofNullable(order.getPlanQuantity())
                                .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                                .orElseThrow(() -> new BaseKnownException("生产任务单计划数量为空"));

                        List<QmHoseAssemblyOrder> qmHoseAssemblyOrders = qmHoseAssemblyOrderMapper.selectList(Wrappers.lambdaQuery(QmHoseAssemblyOrder.class)
                                .eq(QmHoseAssemblyOrder::getWorkOrderCode, order.getCode())
                                .nested(i -> i
                                        .eq(QmHoseAssemblyOrder::getHideEarlierCheckout, 0)
                                        .or()
                                        .isNull(QmHoseAssemblyOrder::getHideEarlierCheckout)
                                )
                                .nested(i -> i
                                        .eq(QmHoseAssemblyOrder::getTestTimes, 1)
                                        .or()
                                        .isNull(QmHoseAssemblyOrder::getTestTimes)
                                )
                        );
                        if (CollectionUtils.isEmpty(qmHoseAssemblyOrders)) {
                            throw new BaseKnownException("软管组件检验单不存在");
                        }

                        submitQuantity = qmHoseAssemblyOrders.stream()
                                .map(QmHoseAssemblyOrder::getQualityPassCount)
                                .filter(Objects::nonNull)
                                .map(BigDecimal::valueOf)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    } catch (Exception ignore) {
                    }

                    if (submitQuantity.compareTo(BigDecimal.ZERO) > 0 && planQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        resp.setProductionPercent(new BigDecimal("100").multiply(submitQuantity).divide(planQuantity, 2, RoundingMode.HALF_UP));
                    }

                    return resp;
                }, executorService))
                .collect(Collectors.toList());

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

        executorService.shutdown();

        List<PmAssemblyKeyOrderResp> resps = allOf
                .thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()))
                .join();

        return new PageImpl<>(resps, pageable, totalElements);
    }

    public PmAssemblyKeyOrderResp getPmAssemblyKeyOrderResp(PmAssemblyWorkOrder order) {
        PmAssemblyKeyOrderResp resp = DtoMapper.convert(order, PmAssemblyKeyOrderResp.class);

        resp.setRawMaterialPercent(BigDecimal.ZERO);
        resp.setProductionPercent(BigDecimal.ZERO);

        try {
            BigDecimal planQuantity = Optional.ofNullable(order.getPlanQuantity())
                    .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                    .orElseThrow(() -> new BaseKnownException("生产任务单计划数量为空"));

            // ExecutorService executorService = Executors.newFixedThreadPool(4);

            int cores = Runtime.getRuntime().availableProcessors();
            ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                    (int)(cores * 1.5) + 1,
                    cores * 2,
                    60,
                    TimeUnit.SECONDS,
                    new ArrayBlockingQueue<>(100),
                    new ThreadPoolExecutor.CallerRunsPolicy()
            );

            // 领料单明细
            CompletableFuture<List<WmDeliverOrderLine>> future1 = CompletableFuture.supplyAsync(() -> {
                try {
                    String deliverOrderCode = Optional.ofNullable(order.getDeliverOrderCode())
                            .orElseThrow(() -> new BaseKnownException("领料单号为空"));

                    Wrapper<WmDeliverOrderLine> wrapper = Wrappers.lambdaQuery(WmDeliverOrderLine.class)
                            .eq(WmDeliverOrderLine::getDeliverOrderCode, deliverOrderCode);

                    return wmDeliverOrderLineMapper.selectList(wrapper);
                } catch (Exception ignore) {
                    return new ArrayList<>();
                }
            }, executorService);

            // BOM
            CompletableFuture<BmBom> future2 = CompletableFuture.supplyAsync(() -> {
                try {
                    int bomId = Optional.ofNullable(order.getBomId())
                            .orElseThrow(() -> new BaseKnownException("BOM ID 为空"));

                    Wrapper<BmBom> wrapper = Wrappers.lambdaQuery(BmBom.class)
                            .eq(BmBom::getId, bomId);

                    return bmBomMapper.selectOne(wrapper);
                } catch (Exception ignore) {
                    return null;
                }
            }, executorService);

            // BOM 子项
            CompletableFuture<List<BmBomItem>> future3 = CompletableFuture.supplyAsync(() -> {
                try {
                    int bomId = Optional.ofNullable(order.getBomId())
                            .orElseThrow(() -> new BaseKnownException("BOM ID 为空"));

                    Wrapper<BmBomItem> wrapper = Wrappers.lambdaQuery(BmBomItem.class)
                            .eq(BmBomItem::getBomId, bomId);

                    return bmBomItemMapper.selectList(wrapper);
                } catch (Exception ignore) {
                    return new ArrayList<>();
                }
            }, executorService);

            // 软管组件检验单
            CompletableFuture<List<QmHoseAssemblyOrder>> future4 = CompletableFuture.supplyAsync(() -> {
                try {
                    String orderCode = Optional.ofNullable(order.getCode())
                            .orElseThrow(() -> new BaseKnownException("订单编码为空"));

                    Wrapper<QmHoseAssemblyOrder> wrapper = Wrappers.lambdaQuery(QmHoseAssemblyOrder.class)
                            .eq(QmHoseAssemblyOrder::getWorkOrderCode, orderCode)
                            .nested(i -> i
                                    .eq(QmHoseAssemblyOrder::getHideEarlierCheckout, 0)
                                    .or()
                                    .isNull(QmHoseAssemblyOrder::getHideEarlierCheckout)
                            )
                            .nested(i -> i
                                    .eq(QmHoseAssemblyOrder::getTestTimes, 1)
                                    .or()
                                    .isNull(QmHoseAssemblyOrder::getTestTimes)
                            );

                    return qmHoseAssemblyOrderMapper.selectList(wrapper);
                } catch (Exception ignore) {
                    return new ArrayList<>();
                }
            }, executorService);

            CompletableFuture<BigDecimal> future11 = future1.thenApplyAsync((lines) -> {
                if (CollectionUtils.isEmpty(lines)) {
                    return BigDecimal.ZERO;
                }

                return lines.stream()
                        .filter(line -> Objects.equals("JSJ", line.getMaterialGroup()))
                        .map(WmDeliverOrderLine::getTargetQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }, executorService);

            CompletableFuture<BigDecimal> future21 = future2.thenCombineAsync(future3, (bom, bomItems) -> {
                try {
                    if (Objects.isNull(bom)) {
                        throw new BaseKnownException("BOM 为不存在");
                    }
                    BigDecimal bomQuantity = Optional.ofNullable(bom.getBomCount())
                            .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                            .orElseThrow(() -> new BaseKnownException("BOM 数量为空"));

                    if (CollectionUtils.isEmpty(bomItems)) {
                        throw new BaseKnownException("BOM 子项为不存在");
                    }
                    List<Integer> materialIds = bomItems.stream()
                            .map(BmBomItem::getMaterialsId)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(materialIds)) {
                        throw new BaseKnownException("BOM 子项对应的物料ID不存在");
                    }

                    Wrapper<BmMaterial> wrapper = Wrappers.lambdaQuery(BmMaterial.class)
                            .in(BmMaterial::getId, materialIds);
                    List<BmMaterial> materials = bmMaterialMapper.selectList(wrapper);
                    if (CollectionUtils.isEmpty(materials)) {
                        throw new BaseKnownException("BOM 子项对应的物料不存在");
                    }

                    List<BmBomItem> jsjBomItems = bomItems.stream()
                            .filter(bomItem -> {
                                boolean flag;

                                try {
                                    BmMaterial material = materials.stream()
                                            .filter(ma -> Objects.equals(ma.getId(), bomItem.getMaterialsId()))
                                            .findFirst()
                                            .orElseThrow(() -> new BaseKnownException("BOM 子项对应的物料不存在"));

                                    flag = Objects.equals("JSJ", material.getMaterialGroupCode());
                                } catch (Exception ignore) {
                                    flag = false;
                                }

                                return flag;
                            })
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(jsjBomItems)) {
                        throw new BaseKnownException("BOM子项中没有金属件");
                    }

                    BigDecimal bomJsjQuantity = jsjBomItems.stream()
                            .map(BmBomItem::getBomItemCount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return planQuantity.multiply(bomJsjQuantity).divide(bomQuantity, 3, RoundingMode.HALF_UP);
                } catch (Exception ignore) {
                    return BigDecimal.ZERO;
                }
            }, executorService);

            CompletableFuture.allOf(future11, future21)
                    .thenAccept(v -> {
                        BigDecimal quantity1 = future11.join();
                        BigDecimal quantity2 = future21.join();

                        resp.setRawMaterialDeliveryQuantity(quantity1);
                        resp.setRawMaterialBomRequireQuantity(quantity2);

                        if (quantity1.compareTo(BigDecimal.ZERO) > 0 && quantity2.compareTo(BigDecimal.ZERO) > 0) {
                            resp.setRawMaterialPercent(new BigDecimal("100").multiply(quantity1).divide(quantity2, 2, RoundingMode.HALF_UP));
                        }
                    })
                    .join();

            future4.thenAccept(list -> {
                        if(CollectionUtils.isEmpty(list)) {
                            resp.setProductionPercent(BigDecimal.ZERO);
                        }

                        BigDecimal submitQuantity = list.stream()
                                .map(QmHoseAssemblyOrder::getQualityPassCount)
                                .filter(Objects::nonNull)
                                .map(BigDecimal::valueOf)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        if (submitQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            resp.setProductionPercent(new BigDecimal("100").multiply(submitQuantity).divide(planQuantity, 2, RoundingMode.HALF_UP));
                        }
                    })
                    .join();

            executorService.shutdown();
        } catch (Exception ignore) {
        }

        return resp;
    }

    // ! 进行中的任务列表 -------------------------------------------------------------------------------------------------

    /**
     * 进行中的任务列表
     */
    public Page<PmAssemblyWorkSubOrderDTO> getPage(PmAssemblyWorkSubOrderPageQueryReq req) {
        return null;
    }

    // ! 瓶颈订单 -------------------------------------------------------------------------------------------------------

    /**
     * 瓶颈订单
     */

    // ! 原料不足情况 ----------------------------------------------------------------------------------------------------

    /**
     * 原料不足情况
     */

    // ! 车间工位人员情况 -------------------------------------------------------------------------------------------------


    // ! 工单进度 -------------------------------------------------------------------------------------------------------

}

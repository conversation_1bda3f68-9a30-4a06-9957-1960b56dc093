package com.hvisions.productBoard.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.productBoard.entity.PmAssemblyWorkOrder;
import com.hvisions.productBoard.entity.QmHoseAssemblyOrder;
import com.hvisions.productBoard.entity.SaSaleOrderLine;
import com.hvisions.productBoard.mapper.*;
import com.hvisions.productBoard.resp.BiOfficeAssemblyOverviewResp;
import com.hvisions.productBoard.resp.BiOfficeOverviewResp;
import com.hvisions.productBoard.resp.BiOfficeTestOverviewResp;
import com.hvisions.productBoard.vo.SaSaleOrderLineVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 生产综合看板 五部办公室看板
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class BiOfficeService {

    private final SaSaleOrderMapper saSaleOrderMapper;
    private final SaSaleOrderLineMapper saSaleOrderLineMapper;

    private final PmAssemblyWorkOrderMapper pmAssemblyWorkOrderMapper;
    private final PmAssemblyWorkSubOrderMapper pmAssemblyWorkSubOrderMapper;

    private final QmHoseAssemblyOrderMapper qmHoseAssemblyOrderMapper;
    private final LmMapper lmMapper;

    // ! 中心区域 -------------------------------------------------------------------------------------------------------

    /**
     * 合同概况
     * 订单总数：还未发货完成单子数量
     * 总根数：未发货完成的单子 未发货根数汇总（部分发货）
     */
    public BiOfficeOverviewResp getOverview() {
        // ExecutorService executorService = Executors.newFixedThreadPool(4);

        int cores = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                (int)(cores * 1.5) + 1,
                cores * 2,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // ! 订单
        CompletableFuture<JSONObject> future1 = CompletableFuture.supplyAsync(this::getOverviewOfOrderTotal, executorService);
        // ! 临期订单
        CompletableFuture<JSONObject> future2 = CompletableFuture.supplyAsync(this::getOverviewOfOrderNear, executorService);
        // ! 异常订单
        CompletableFuture<JSONObject> future3 = CompletableFuture.supplyAsync(this::getOverviewOfOrderError, executorService);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(future1, future2, future3);

        executorService.shutdown();

        return allOf
                .thenApply(v -> {
                    BiOfficeOverviewResp resp = new BiOfficeOverviewResp();

                    JSONObject json1 = future1.join();
                    resp.setOrderCount(json1.getLong("count"));
                    resp.setOrderQuantity(json1.getBigDecimal("quantity"));

                    JSONObject json2 = future2.join();
                    resp.setNearOrderCount(json2.getLong("count"));
                    resp.setNearOrderQuantity(json2.getBigDecimal("quantity"));

                    JSONObject json3 = future3.join();
                    resp.setErrorOrderCount(json3.getLong("count"));
                    resp.setErrorOrderQuantity(json3.getBigDecimal("quantity"));

                    return resp;
                })
                .join();
    }

    public JSONObject getOverviewOfOrderTotal() {
        JSONObject json = new JSONObject();

        long count = 0L;
        BigDecimal quantity = BigDecimal.ZERO;

        try {
            List<SaSaleOrderLine> list = saSaleOrderLineMapper.selectList(
                    Wrappers.lambdaQuery(SaSaleOrderLine.class)
                            .eq(SaSaleOrderLine::getDepartment, "SY05")
                            .notIn(SaSaleOrderLine::getShippingState, 20, 40)
            );

            if (CollectionUtils.isNotEmpty(list)) {
                Map<String, List<SaSaleOrderLine>> saleOrderCodeMap = list.stream()
                        .collect(Collectors.groupingBy(SaSaleOrderLine::getSaleOrderCode));

                count = saleOrderCodeMap.size();

                BigDecimal total1 = list.stream()
                        .map(SaSaleOrderLine::getPlanCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal total2 = list.stream()
                        .map(SaSaleOrderLine::getDeliverCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                quantity = total1.subtract(total2);
            }
        } catch (Exception ignore) {
        } finally {
            json.put("count", count);
            json.put("quantity", quantity);
        }

        return json;
    }

    public JSONObject getOverviewOfOrderNear() {
        JSONObject json = new JSONObject();

        long count = 0L;
        BigDecimal quantity = BigDecimal.ZERO;

        try {
            LocalDate today = LocalDate.now();

            List<SaSaleOrderLine> list = saSaleOrderLineMapper.selectList(
                    Wrappers.lambdaQuery(SaSaleOrderLine.class)
                            .ge(SaSaleOrderLine::getDeliverDate, today)
                            .le(SaSaleOrderLine::getDeliverDate, today.plusDays(30))
                            .eq(SaSaleOrderLine::getDepartment, "SY05")
                            .notIn(SaSaleOrderLine::getShippingState, 20, 40)
            );

            if (CollectionUtils.isNotEmpty(list)) {
                Map<String, List<SaSaleOrderLine>> saleOrderCodeMap = list.stream()
                        .collect(Collectors.groupingBy(SaSaleOrderLine::getSaleOrderCode));

                count = saleOrderCodeMap.size();

                BigDecimal total1 = list.stream()
                        .map(SaSaleOrderLine::getPlanCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal total2 = list.stream()
                        .map(SaSaleOrderLine::getDeliverCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                quantity = total1.subtract(total2);
            }
        } catch (Exception ignore) {
        } finally {
            json.put("count", count);
            json.put("quantity", quantity);
        }

        return json;
    }

    public JSONObject getOverviewOfOrderError() {
        JSONObject json = new JSONObject();

        long count = 0L;
        BigDecimal quantity = BigDecimal.ZERO;

        try {
            LocalDate today = LocalDate.now();

            List<SaSaleOrderLine> list = saSaleOrderLineMapper.selectList(
                    Wrappers.lambdaQuery(SaSaleOrderLine.class)
                            .le(SaSaleOrderLine::getDeliverDate, today)
                            .eq(SaSaleOrderLine::getDepartment, "SY05")
                            .notIn(SaSaleOrderLine::getShippingState, 20, 40)
            );

            if (CollectionUtils.isNotEmpty(list)) {
                Map<String, List<SaSaleOrderLine>> saleOrderCodeMap = list.stream()
                        .collect(Collectors.groupingBy(SaSaleOrderLine::getSaleOrderCode));

                count = saleOrderCodeMap.size();

                BigDecimal total1 = list.stream()
                        .map(SaSaleOrderLine::getPlanCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal total2 = list.stream()
                        .map(SaSaleOrderLine::getDeliverCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                quantity = total1.subtract(total2);
            }
        } catch (Exception ignore) {
        } finally {
            json.put("count", count);
            json.put("quantity", quantity);
        }

        return json;
    }

    // ? 临期订单(进度，待工厂确定每一步权重)
    public List<SaSaleOrderLineVO> getNearOrderList() {
        LocalDate today = LocalDate.now();

        List<SaSaleOrderLine> list = saSaleOrderLineMapper.selectList(
                Wrappers.lambdaQuery(SaSaleOrderLine.class)
                        .eq(SaSaleOrderLine::getDepartment, "SY05")
                        .ge(SaSaleOrderLine::getDeliverDate, today.atTime(0, 0, 0))
                        .le(SaSaleOrderLine::getDeliverDate, today.plusDays(30).atTime(23, 59, 59))
                        .notIn(SaSaleOrderLine::getShippingState, 20, 40)
        );

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        Map<String, List<SaSaleOrderLine>> saleOrderCodeMap = list.stream()
                .filter(line -> Objects.nonNull(line.getSaleOrderCode()))
                .collect(Collectors.groupingBy(line -> {
                    String code = line.getSaleOrderCode().replaceAll(" ", "");

                    if (code.contains("-05-")) {
                        code = code.replace("-05-", "-");
                    }
                    if (code.contains("-05部-")) {
                        code = code.replace("-05部-", "-");
                    }
                    if (code.contains("-05部")) {
                        code = code.replace("-05部", "-");
                    }

                    String[] arr = code.split("-");

                    if (arr.length <= 2) {
                        return code;
                    }

                    return String.join("-", arr[0], arr[1]);
                }));

        return saleOrderCodeMap.entrySet().stream()
                .map(entry -> {
                    SaSaleOrderLine saSaleOrderLine = entry.getValue().get(0);

                    saSaleOrderLine.setSaleOrderCode(entry.getKey());

                    return DtoMapper.convert(saSaleOrderLine, SaSaleOrderLineVO.class);
                })
                .collect(Collectors.toList());
    }

    // ? 异常订单(异常原因待MES补充)
    public List<SaSaleOrderLineVO> getErrorOrderList() {
        LocalDate today = LocalDate.now();

        List<SaSaleOrderLine> list = saSaleOrderLineMapper.selectList(
                Wrappers.lambdaQuery(SaSaleOrderLine.class)
                        .eq(SaSaleOrderLine::getDepartment, "SY05")
                        .lt(SaSaleOrderLine::getDeliverDate, today.atTime(0, 0, 0))
                        .notIn(SaSaleOrderLine::getShippingState, 20, 40)
        );

        Map<String, List<SaSaleOrderLine>> saleOrderCodeMap = list.stream()
                .filter(line -> Objects.nonNull(line.getSaleOrderCode()))
                .collect(Collectors.groupingBy(line -> {
                    String code = line.getSaleOrderCode().replaceAll(" ", "");

                    if (code.contains("-05-")) {
                        code = code.replace("-05-", "-");
                    }
                    if (code.contains("-05部-")) {
                        code = code.replace("-05部-", "-");
                    }
                    if (code.contains("-05部")) {
                        code = code.replace("-05部", "-");
                    }

                    String[] arr = code.split("-");

                    if (arr.length <= 2) {
                        return code;
                    }

                    return String.join("-", arr[0], arr[1]);
                }));

        return saleOrderCodeMap.entrySet().stream()
                .map(entry -> {
                    SaSaleOrderLine saSaleOrderLine = entry.getValue().get(0);

                    saSaleOrderLine.setSaleOrderCode(entry.getKey());

                    return DtoMapper.convert(saSaleOrderLine, SaSaleOrderLineVO.class);
                })
                .collect(Collectors.toList());
    }

    // ! 总装班组 -------------------------------------------------------------------------------------------------------

    /**
     * 订单情况（总装生产任务单）
     * 订单（还未完成的总装生产任务单）
     * 总数：任务单数量
     * 总根数：总未生产完成根数
     * 临期订单（距离交货日期不足一个月的订单）
     * 总数：订单数量
     * 总根数：总未生产完成根数
     * 异常订单（超出交货日期的订单）
     * 总数：订单数量
     * 总根数：总未生产完成根数
     */
    public BiOfficeAssemblyOverviewResp getAssemblyOverview() {
        // ExecutorService executorService = Executors.newFixedThreadPool(4);

        int cores = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                (int)(cores * 1.5) + 1,
                cores * 2,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        CompletableFuture<JSONObject> future1 = CompletableFuture.supplyAsync(this::getAssemblyOverviewOfOrderTotal, executorService);
        CompletableFuture<JSONObject> future2 = CompletableFuture.supplyAsync(this::getAssemblyOverviewOfOrderNear, executorService);
        CompletableFuture<JSONObject> future3 = CompletableFuture.supplyAsync(this::getAssemblyOverviewOfOrderError, executorService);

        CompletableFuture<BiOfficeAssemblyOverviewResp> future = CompletableFuture.allOf(future1, future2, future3).thenApplyAsync(
                (v) -> {
                    BiOfficeAssemblyOverviewResp resp = new BiOfficeAssemblyOverviewResp();

                    JSONObject json1 = future1.join();
                    resp.setOrderCount(json1.getLong("count"));
                    resp.setOrderQuantity(json1.getBigDecimal("quantity"));

                    JSONObject json2 = future2.join();
                    resp.setNearOrderCount(json2.getLong("count"));
                    resp.setNearOrderQuantity(json2.getBigDecimal("quantity"));

                    JSONObject json3 = future3.join();
                    resp.setErrorOrderCount(json3.getLong("count"));
                    resp.setErrorOrderQuantity(json3.getBigDecimal("quantity"));

                    return resp;
                },
                executorService
        );

        BiOfficeAssemblyOverviewResp resp = future.join();

        executorService.shutdown();

        // BiOfficeAssemblyOverviewResp resp = new BiOfficeAssemblyOverviewResp();
        //
        // // 订单
        // JSONObject json1 = getAssemblyOverviewOfOrderTotal();
        //
        // resp.setOrderCount(json1.getLong("count"));
        // resp.setOrderQuantity(json1.getBigDecimal("quantity"));
        //
        // // 临期订单
        // JSONObject json2 = getAssemblyOverviewOfOrderNear();
        //
        // resp.setNearOrderCount(json2.getLong("count"));
        // resp.setNearOrderQuantity(json2.getBigDecimal("quantity"));
        //
        // // 异常订单
        // JSONObject json3 = getAssemblyOverviewOfOrderError();
        //
        // resp.setErrorOrderCount(json3.getLong("count"));
        // resp.setErrorOrderQuantity(json3.getBigDecimal("quantity"));

        return resp;
    }

    public JSONObject getAssemblyOverviewOfOrderTotal() {
        JSONObject json = new JSONObject();

        long count = 0L;
        BigDecimal quantity = BigDecimal.ZERO;

        try {
            List<PmAssemblyWorkOrder> list = pmAssemblyWorkOrderMapper.selectList(
                    Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                            .in(PmAssemblyWorkOrder::getState, 0, 10)
            );

            if (CollectionUtils.isNotEmpty(list)) {
                count = list.size();

                BigDecimal total1 = list.stream()
                        .map(PmAssemblyWorkOrder::getPlanQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal total2 = list.stream()
                        .map(PmAssemblyWorkOrder::getProductQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                quantity = total1.subtract(total2);
            }
        } catch (Exception ignore) {
        } finally {
            json.put("count", count);
            json.put("quantity", quantity);
        }

        return json;
    }

    public JSONObject getAssemblyOverviewOfOrderNear() {
        JSONObject json = new JSONObject();

        long count = 0;
        BigDecimal quantity = BigDecimal.ZERO;

        try {
            LocalDate today = LocalDate.now();

            Wrapper<PmAssemblyWorkOrder> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                    .ge(PmAssemblyWorkOrder::getPlanEndTime, today)
                    .le(PmAssemblyWorkOrder::getPlanEndTime, today.plusDays(30))
                    .in(PmAssemblyWorkOrder::getState, 0, 10);

            List<PmAssemblyWorkOrder> list = pmAssemblyWorkOrderMapper.selectList(wrapper);

            if (CollectionUtils.isNotEmpty(list)) {
                count = list.size();

                BigDecimal total1 = list.stream()
                        .map(PmAssemblyWorkOrder::getPlanQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal total2 = list.stream()
                        .map(PmAssemblyWorkOrder::getProductQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                quantity = total1.subtract(total2);
            }
        } catch (Exception ignore) {
        } finally {
            json.put("count", count);
            json.put("quantity", quantity);
        }

        return json;
    }

    public JSONObject getAssemblyOverviewOfOrderError() {
        JSONObject json = new JSONObject();

        long count = 0;
        BigDecimal quantity = BigDecimal.ZERO;

        try {
            LocalDate today = LocalDate.now();

            Wrapper<PmAssemblyWorkOrder> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                    .lt(PmAssemblyWorkOrder::getPlanEndTime, today)
                    .in(PmAssemblyWorkOrder::getState, 0, 10);

            List<PmAssemblyWorkOrder> list = pmAssemblyWorkOrderMapper.selectList(wrapper);

            if (CollectionUtils.isNotEmpty(list)) {
                count = list.size();

                BigDecimal total1 = list.stream()
                        .map(PmAssemblyWorkOrder::getPlanQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal total2 = list.stream()
                        .map(PmAssemblyWorkOrder::getProductQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                quantity = total1.subtract(total2);
            }
        } catch (Exception ignore) {
        } finally {
            json.put("count", count);
            json.put("quantity", quantity);
        }

        return json;
    }

    // ! 增强班组 -------------------------------------------------------------------------------------------------------

    // ! 成型班组 -------------------------------------------------------------------------------------------------------

    // ! 试验班组 -------------------------------------------------------------------------------------------------------

    // ? 订单概况
    // ? 试验订单（LIMS执行中的软管组件检验单）
    // ? 软管组件检验单 (待复核、新建状态)
    public BiOfficeTestOverviewResp getTestOverview() {
        BiOfficeTestOverviewResp resp = new BiOfficeTestOverviewResp();

        // 试验订单
        long testOrderCount = 0L;
        BigDecimal testOrderQuantity = BigDecimal.ZERO;

        try {
            testOrderCount = lmMapper.selectCountOfWorkingContract();
        } catch (Exception ignore) {
        }
        try {
            testOrderQuantity = lmMapper.selectQuantityOfWorkingContract();
        } catch (Exception ignore) {
        }

        // 耐压订单
        long pressureOrderCount = 0L;
        BigDecimal pressureOrderQuantity = BigDecimal.ZERO;

        try {
            List<QmHoseAssemblyOrder> list = qmHoseAssemblyOrderMapper.selectList(
                    Wrappers.lambdaQuery(QmHoseAssemblyOrder.class)
                            .in(QmHoseAssemblyOrder::getQualityState, 0, 5)
            );

            if (CollectionUtils.isNotEmpty(list)) {
                pressureOrderCount = list.size();

                testOrderQuantity = list.stream()
                        .map(QmHoseAssemblyOrder::getTotalCount)
                        .filter(Objects::nonNull)
                        .map(BigDecimal::valueOf)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        } catch (Exception ignore) {
        }

        resp.setTestOrderCount(testOrderCount);
        resp.setTestOrderQuantity(testOrderQuantity);

        resp.setPressureOrderCount(pressureOrderCount);
        resp.setPressureOrderQuantity(pressureOrderQuantity);

        resp.setOrderCount(testOrderCount + pressureOrderCount);
        resp.setOrderQuantity(testOrderQuantity.add(pressureOrderQuantity));

        return resp;
    }

    // ? 试验完成统计

    // ? 耐压完成统计（试验完成状态）


    // ! 质检班组 -------------------------------------------------------------------------------------------------------

    // ! 保障班组 -------------------------------------------------------------------------------------------------------

}

package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.productBoard.entity.*;
import com.hvisions.productBoard.mapper.*;
import com.hvisions.productBoard.req.BiAssemblyKeyOrderReq;
import com.hvisions.productBoard.resp.BiAssemblyKeyOrderResp;
import com.hvisions.productBoard.resp.BiAssemblyPlanStatisticResp;
import com.hvisions.productBoard.resp.BiAssemblyStepFinishedQuantityLastAndCurrWeekResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 生产综合看板 五部总装看板
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class BiAssemblyService {

    private final BmMaterialMapper bmMaterialMapper;

    private final BmBomMapper bmBomMapper;
    private final BmBomItemMapper bmBomItemMapper;

    private final WmDeliverOrderMapper wmDeliverOrderMapper;
    private final WmDeliverOrderLineMapper wmDeliverOrderLineMapper;

    private final PmAssemblyWorkOrderMapper pmAssemblyWorkOrderMapper;
    private final PmAssemblyWorkSubOrderMapper pmAssemblyWorkSubOrderMapper;
    private final PmAssemblyWorkSubOrderOperationOutputMapper pmAssemblyWorkSubOrderOperationOutputMapper;

    private final SaSaleOrderMapper saSaleOrderMapper;
    private final SaSaleOrderLineMapper saSaleOrderLineMapper;

    private final QmHoseAssemblyOrderMapper qmHoseAssemblyOrderMapper;
    private final QmHoseAssemblyOrderLineMapper qmHoseAssemblyOrderLineMapper;

    /**
     * 工位任务生产情况【需要提供工单清单】
     */
    public List<BiAssemblyStepFinishedQuantityLastAndCurrWeekResp> getStepFinishedQuantityOfLastAndCurrWeek() {
        List<String> stepNames = Lists.newArrayList(
                "落料",
                "内衬与接头连接",
                "清洁",
                "内管切割",
                "接头装配",
                "登记",
                "扣压关键工序",
                "收缩套",
                "防护套",
                "螺母装配",
                "标牌制作",
                "自检",
                "软管组件复验"
        );

        List<BiAssemblyStepFinishedQuantityLastAndCurrWeekResp> list = stepNames.stream()
                .map(stepName -> {
                    BiAssemblyStepFinishedQuantityLastAndCurrWeekResp resp = new BiAssemblyStepFinishedQuantityLastAndCurrWeekResp();

                    resp.setStepName(stepName);
                    resp.setFinishedQuantityOfLastWeek(BigDecimal.ZERO);
                    resp.setFinishedQuantityOfCurrWeek(BigDecimal.ZERO);

                    return resp;
                })
                .collect(Collectors.toList());

        LocalDate today = LocalDate.now();

        LocalDateTime startOfLastWeek = today.minusWeeks(1).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atTime(0, 0, 0);
        LocalDateTime endOfLastWeek = today.minusWeeks(1).with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59, 59);
        List<PmAssemblyWorkSubOrderOperationOutput> list1 = pmAssemblyWorkSubOrderOperationOutputMapper.selectListByCreateTimeBetween(startOfLastWeek, endOfLastWeek);
        if (CollectionUtils.isNotEmpty(list1)) {
            list.forEach(resp -> {
                BigDecimal quantity = list1.stream()
                        .filter(output -> Objects.equals(resp.getStepName(), output.getOperationName()))
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                resp.setFinishedQuantityOfLastWeek(quantity);
            });
        }

        LocalDateTime startOfCurrWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).atTime(0, 0, 0);
        LocalDateTime endOfCurrWeek = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).atTime(23, 59, 59);
        List<PmAssemblyWorkSubOrderOperationOutput> list2 = pmAssemblyWorkSubOrderOperationOutputMapper.selectListByCreateTimeBetween(startOfCurrWeek, endOfCurrWeek);
        if (CollectionUtils.isNotEmpty(list2)) {
            list.forEach(resp -> {
                BigDecimal quantity = list2.stream()
                        .filter(output -> Objects.equals(resp.getStepName(), output.getOperationName()))
                        .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                resp.setFinishedQuantityOfCurrWeek(quantity);
            });
        }

        return list;
    }

    /**
     * 关键订单生产状态实时监控
     * 紧急生产任务单 超过10条 翻页显示，不超过10个取未完成的生产任务单（按创建日期顺序）补充至10条
     * 没有紧急生产任务单，按照创建日期顺序取10条未完成的生产任务单，不需要翻页展示
     * 原料准备：领料单金属件总数量 / 该生产任务单需求金属件总数量
     * 生产执行：完成数量（非提前检验的软管组件检验单提交根数）/ 生产任务单总数量
     */
    public Page<BiAssemblyKeyOrderResp> getKeyOrders(BiAssemblyKeyOrderReq req) {
        Page<PmAssemblyWorkOrder> page = PageHelperUtil.getPage(pmAssemblyWorkOrderMapper::selectListByBiAssemblyKeyOrderReq, req);

        Pageable pageable = page.getPageable();

        long totalElements = page.getTotalElements();
        int pageSize = pageable.getPageSize();

        List<PmAssemblyWorkOrder> list1 = page.getContent();
        if (CollectionUtils.isEmpty(list1)) {
            List<PmAssemblyWorkOrder> list2 = pmAssemblyWorkOrderMapper.selectListByStateIngAndLimitSize(pageSize);
            if (CollectionUtils.isNotEmpty(list2)) {
                list1.addAll(list2);

                totalElements = totalElements + list2.size();
            }
        } else if (list1.size() < pageSize) {
            List<PmAssemblyWorkOrder> list2 = pmAssemblyWorkOrderMapper.selectListByStateIngAndLimitSize(pageSize - list1.size());
            if (CollectionUtils.isNotEmpty(list2)) {
                list1.addAll(list2);

                totalElements = totalElements + list2.size();
            }
        }

        // ExecutorService executorService = Executors.newFixedThreadPool(10);

        int cores = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                (int)(cores * 1.5) + 1,
                cores * 2,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        List<CompletableFuture<BiAssemblyKeyOrderResp>> futures = list1.stream()
                .map(item -> CompletableFuture.supplyAsync(
                        () -> {
                            BiAssemblyKeyOrderResp resp = new BiAssemblyKeyOrderResp();

                            resp.setWorkOrderCode(item.getCode());
                            resp.setProductModel(item.getProductModel());
                            resp.setRawMaterialPercent(BigDecimal.ZERO);
                            resp.setProductionPercent(BigDecimal.ZERO);

                            BigDecimal rawMaterialPrepareQuantity = BigDecimal.ZERO;

                            try {
                                String deliverOrderCode = Optional.ofNullable(item.getDeliverOrderCode())
                                        .orElseThrow(() -> new BaseKnownException("领料单号为空"));

                                List<WmDeliverOrderLine> deliverOrderLines = wmDeliverOrderLineMapper.selectList(
                                        Wrappers.lambdaQuery(WmDeliverOrderLine.class)
                                                .eq(WmDeliverOrderLine::getDeliverOrderCode, deliverOrderCode)
                                );
                                if (CollectionUtils.isEmpty(deliverOrderLines)) {
                                    throw new BaseKnownException("领料单明细为空");
                                }

                                rawMaterialPrepareQuantity = deliverOrderLines.stream()
                                        .filter(line -> Objects.equals("JSJ", line.getMaterialGroup()))
                                        .map(WmDeliverOrderLine::getTargetQuantity)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            } catch (Exception ignore) {
                            }

                            BigDecimal rawMaterialTotalQuantity = BigDecimal.ZERO;
                            try {
                                BigDecimal planQuantity = Optional.ofNullable(item.getPlanQuantity())
                                        .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                                        .orElseThrow(() -> new BaseKnownException("生产任务单计划数量为空"));

                                Integer bomId = Optional.ofNullable(item.getBomId())
                                        .orElseThrow(() -> new BaseKnownException("BOM ID 为空"));

                                BmBom bom = bmBomMapper.selectOne(Wrappers.lambdaQuery(BmBom.class).eq(BmBom::getId, bomId));
                                if (Objects.isNull(bom)) {
                                    throw new BaseKnownException("BOM 为不存在");
                                }

                                BigDecimal bomQuantity = Optional.ofNullable(bom.getBomCount())
                                        .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                                        .orElseThrow(() -> new BaseKnownException("BOM 数量为空"));

                                List<BmBomItem> bomItems = bmBomItemMapper.selectList(Wrappers.lambdaQuery(BmBomItem.class).eq(BmBomItem::getBomId, bomId));
                                if (CollectionUtils.isEmpty(bomItems)) {
                                    throw new BaseKnownException("BOM 子项为不存在");
                                }

                                Set<Integer> materialIds = bomItems.stream()
                                        .map(BmBomItem::getMaterialsId)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toSet());
                                if (CollectionUtils.isEmpty(materialIds)) {
                                    throw new BaseKnownException("BOM 子项对应的物料ID不存在");
                                }

                                List<BmMaterial> materials = bmMaterialMapper.selectList(Wrappers.lambdaQuery(BmMaterial.class).in(BmMaterial::getId, materialIds));
                                if (CollectionUtils.isEmpty(materials)) {
                                    throw new BaseKnownException("BOM 子项对应的物料不存在");
                                }

                                List<BmBomItem> jsjBomItems = bomItems.stream()
                                        .filter(bomItem -> {
                                            boolean flag;

                                            try {
                                                BmMaterial material = materials.stream()
                                                        .filter(ma -> Objects.equals(ma.getId(), bomItem.getMaterialsId()))
                                                        .findFirst()
                                                        .orElseThrow(() -> new BaseKnownException("BOM 子项对应的物料不存在"));

                                                flag = Objects.equals("JSJ", material.getMaterialGroupCode());
                                            } catch (Exception ignore) {
                                                flag = false;
                                            }

                                            return flag;
                                        })
                                        .collect(Collectors.toList());
                                if (CollectionUtils.isEmpty(jsjBomItems)) {
                                    throw new BaseKnownException("BOM子项中没有金属件");
                                }

                                BigDecimal bomJsjQuantity = jsjBomItems.stream()
                                        .map(BmBomItem::getBomItemCount)
                                        .filter(Objects::nonNull)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                                rawMaterialTotalQuantity = planQuantity.multiply(bomJsjQuantity).divide(bomQuantity, 3, RoundingMode.HALF_UP);
                            } catch (Exception ignore) {
                            }

                            if (rawMaterialPrepareQuantity.compareTo(BigDecimal.ZERO) > 0 && rawMaterialTotalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                                resp.setRawMaterialPercent(new BigDecimal("100").multiply(rawMaterialPrepareQuantity).divide(rawMaterialTotalQuantity, 2, RoundingMode.HALF_UP));
                            }

                            // 生产执行
                            BigDecimal submitQuantity = BigDecimal.ZERO;
                            BigDecimal planQuantity = BigDecimal.ZERO;

                            try {
                                planQuantity = Optional.ofNullable(item.getPlanQuantity())
                                        .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                                        .orElseThrow(() -> new BaseKnownException("生产任务单计划数量为空"));

                                List<QmHoseAssemblyOrder> qmHoseAssemblyOrders = qmHoseAssemblyOrderMapper.selectList(Wrappers.lambdaQuery(QmHoseAssemblyOrder.class)
                                        .eq(QmHoseAssemblyOrder::getWorkOrderCode, item.getCode())
                                        .nested(i -> i
                                                .eq(QmHoseAssemblyOrder::getHideEarlierCheckout, 0)
                                                .or()
                                                .isNull(QmHoseAssemblyOrder::getHideEarlierCheckout)
                                        )
                                        .nested(i -> i
                                                .eq(QmHoseAssemblyOrder::getTestTimes, 1)
                                                .or()
                                                .isNull(QmHoseAssemblyOrder::getTestTimes)
                                        )
                                );
                                if (CollectionUtils.isEmpty(qmHoseAssemblyOrders)) {
                                    throw new BaseKnownException("软管组件检验单不存在");
                                }

                                submitQuantity = qmHoseAssemblyOrders.stream()
                                        .map(QmHoseAssemblyOrder::getQualityPassCount)
                                        .filter(Objects::nonNull)
                                        .map(BigDecimal::valueOf)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                            } catch (Exception ignore) {
                            }

                            if (submitQuantity.compareTo(BigDecimal.ZERO) > 0 && planQuantity.compareTo(BigDecimal.ZERO) > 0) {
                                resp.setProductionPercent(new BigDecimal("100").multiply(submitQuantity).divide(planQuantity, 2, RoundingMode.HALF_UP));
                            }

                            return resp;
                        },
                        executorService
                ))
                .collect(Collectors.toList());

        CompletableFuture<List<BiAssemblyKeyOrderResp>> results = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
        List<BiAssemblyKeyOrderResp> resps = results.join();

        executorService.shutdown();

        return new PageImpl<>(resps, pageable, totalElements);
    }

    /**
     * 本月需完成计划
     * 订单数：计划完成日期在本月的订单总数
     * 产品量：计划完成日期在本月的且状态为完成的订单总数
     */
    public BiAssemblyPlanStatisticResp getPlanStatisticOfCurrMonth() {
        BiAssemblyPlanStatisticResp resp = new BiAssemblyPlanStatisticResp();

        LocalDate today = LocalDate.now();

        LocalDateTime start = today.withDayOfMonth(1).atTime(0, 0, 0);
        LocalDateTime end = today.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);

        long finishedOrderCount = 0L;
        BigDecimal finishedOrderQuantity = BigDecimal.ZERO;
        try {
            LambdaQueryWrapper<PmAssemblyWorkOrder> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                    .ge(PmAssemblyWorkOrder::getPlanEndTime, start)
                    .le(PmAssemblyWorkOrder::getPlanStartTime, end)
                    .eq(PmAssemblyWorkOrder::getState, 20);

            List<PmAssemblyWorkOrder> list = pmAssemblyWorkOrderMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                finishedOrderCount = list.size();

                finishedOrderQuantity = list.stream()
                        .map(PmAssemblyWorkOrder::getProductQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        } catch (Exception e) {
            log.error("getCurrMonthPlan--->finished", e);
        } finally {
            resp.setFinishedOrderCount(finishedOrderCount);
            resp.setFinishedOrderQuantity(finishedOrderQuantity);
        }

        long totalOrderCount = 0L;
        BigDecimal totalOrderQuantity = BigDecimal.ZERO;
        try {
            LambdaQueryWrapper<PmAssemblyWorkOrder> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                    .ge(PmAssemblyWorkOrder::getPlanEndTime, start)
                    .le(PmAssemblyWorkOrder::getPlanStartTime, end);

            List<PmAssemblyWorkOrder> list = pmAssemblyWorkOrderMapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(list)) {
                totalOrderCount = list.size();

                totalOrderQuantity = list.stream()
                        .map(PmAssemblyWorkOrder::getPlanQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        } catch (Exception e) {
            log.error("getCurrMonthPlan--->total", e);
        } finally {
            resp.setTotalOrderCount(totalOrderCount);
            resp.setTotalOrderQuantity(totalOrderQuantity);
        }

        return resp;
    }

    // ? 硫化后返厂代检订单【需要先完成MES改动】只显示返厂待检状态的订单

    // ? 通知列表
}

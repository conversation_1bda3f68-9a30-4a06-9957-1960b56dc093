package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.productBoard.entity.WmsMaterialStocks;
import com.hvisions.productBoard.mapper.WmsMaterialBatchStocksMapper;
import com.hvisions.productBoard.mapper.WmsMaterialStocksMapper;
import com.hvisions.productBoard.materialStocks.dto.MaterialStockQueryDTO;
import com.hvisions.productBoard.materialStocks.vo.WmsMaterialStocksVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class WmsMaterialStocksService {

    @Resource
    private WmsMaterialStocksMapper wmsMaterialStocksMapper;
    @Resource
    private WmsMaterialBatchStocksMapper wmsMaterialBatchStocksMapper;

    public List<WmsMaterialStocksVO> query(MaterialStockQueryDTO query) {
        List<WmsMaterialStocks> stocks = wmsMaterialStocksMapper.selectList(
                new QueryWrapper<WmsMaterialStocks>().lambda()
                        .in(CollectionUtils.isNotEmpty(query.getMaterialCodes()), WmsMaterialStocks::getMaterialCode, query.getMaterialCodes())
                        .in(CollectionUtils.isNotEmpty(query.getMaterialTypeCodes()), WmsMaterialStocks::getMaterialTypeCode, query.getMaterialTypeCodes())
        );
        List<WmsMaterialStocksVO> rs = DtoMapper.convertList(stocks, WmsMaterialStocksVO.class);
        return rs;
    }
}

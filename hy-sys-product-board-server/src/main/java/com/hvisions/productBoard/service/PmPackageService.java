package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.productBoard.entity.HyPmPkgOrder;
import com.hvisions.productBoard.entity.HyWmsCompleteDeliverOrder;
import com.hvisions.productBoard.enums.CompleteDeliverOrderStateEnum;
import com.hvisions.productBoard.enums.PackageIndexTypeEnum;
import com.hvisions.productBoard.enums.PackageWorkOrderStateEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.HyPmPkgOrderMapper;
import com.hvisions.productBoard.mapper.HyWmsCompleteDeliverOrderMapper;
import com.hvisions.productBoard.resp.PmPackageCompleteOutputResp;
import com.hvisions.productBoard.resp.PmPackageDataOverviewResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

@Slf4j
@Service
@RequiredArgsConstructor
public class PmPackageService {

    @Resource
    private HyPmPkgOrderMapper hyPmPkgOrderMapper;
    @Resource
    private HyWmsCompleteDeliverOrderMapper hyWmsCompleteDeliverOrderMapper;


    public PmPackageDataOverviewResp getDataOverview() {

        List<HyPmPkgOrder> orders = hyPmPkgOrderMapper.selectList(new QueryWrapper<HyPmPkgOrder>().lambda()
                .eq(HyPmPkgOrder::getState, PackageWorkOrderStateEnum.NEW));

        Integer orderCount = orders.size();
        BigDecimal orderQuantity = orders.stream()
                .map(HyPmPkgOrder::getQuantity)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // TODO 紧急订单逻辑暂无

        return PmPackageDataOverviewResp.builder()
                .orderCount(orderCount)
                .orderQuantity(orderQuantity)
                .errorOrderCount(0)
                .errorOrderQuantity(BigDecimal.ZERO)
                .build();
    }

    public List<PmPackageCompleteOutputResp> getOutput(TimePeriodEnum periodEnum, PackageIndexTypeEnum packageIndexTypeEnum) {

        List<PmPackageCompleteOutputResp> resultList = new ArrayList<>();
        LocalDate today = LocalDate.now();

        switch (periodEnum) {
            case YEAR:
                int currentYear = today.getYear();
                for (int month = 1; month <= 12; month++) {
                    LocalDate startDate = LocalDate.of(currentYear, month, 1);
                    LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
                    String dateLabel = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                    resultList.add(calculateOutputForPeriod(startDate, endDate, dateLabel, periodEnum.getName(), packageIndexTypeEnum));
                }
                break;
            case MONTH:
                LocalDate monthStart = today.withDayOfMonth(1);
                LocalDate monthEnd = today.withDayOfMonth(today.lengthOfMonth());
                
                for (int week = 1; week <= 4; week++) {
                    LocalDate weekStart = monthStart.plusWeeks(week - 1);
                    LocalDate weekEnd = weekStart.plusDays(6);
                    
                    if (weekStart.isBefore(monthStart)) {
                        weekStart = monthStart;
                    }
                    if (weekEnd.isAfter(monthEnd)) {
                        weekEnd = monthEnd;
                    }
                    
                    String dateLabel = "第" + week + "周";
                    resultList.add(calculateOutputForPeriod(weekStart, weekEnd, dateLabel, periodEnum.getName(), packageIndexTypeEnum));
                }
                break;
            case WEEK:
                LocalDate weekStart = today.with(WeekFields.of(Locale.getDefault()).dayOfWeek(), 1);
                for (int day = 0; day < 7; day++) {
                    LocalDate date = weekStart.plusDays(day);
                    String dateStr = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
                    resultList.add(calculateOutputForPeriod(date, date, dateStr, periodEnum.getName(), packageIndexTypeEnum));
                }
                break;
            default:
                throw new IllegalArgumentException("不支持此时间维度: " + periodEnum);
        }

        return resultList;
    }

    private PmPackageCompleteOutputResp calculateOutputForPeriod(LocalDate startDate, LocalDate endDate, String dateStr, String periodName, PackageIndexTypeEnum packageIndexTypeEnum) {
        BigDecimal totalOutput = BigDecimal.ZERO;
        BigDecimal totalOrderCount = BigDecimal.ZERO;

        if (packageIndexTypeEnum == PackageIndexTypeEnum.PACKAGE) {
            List<HyPmPkgOrder> orders = hyPmPkgOrderMapper.selectList(
                    new QueryWrapper<HyPmPkgOrder>().lambda()
                            .isNotNull(HyPmPkgOrder::getPkgDate)
                            .between(HyPmPkgOrder::getPkgDate, startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay())
                            .eq(HyPmPkgOrder::getState, PackageWorkOrderStateEnum.PACKAGED.getValue())
            );

            totalOrderCount = BigDecimal.valueOf(orders.size());
            totalOutput = orders.stream()
                    .map(order -> order.getQuantity() != null ? new BigDecimal(order.getQuantity()) : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } else if (packageIndexTypeEnum == PackageIndexTypeEnum.STORAGE) {
            List<HyWmsCompleteDeliverOrder> orders = hyWmsCompleteDeliverOrderMapper.selectList(
                    new QueryWrapper<HyWmsCompleteDeliverOrder>().lambda()
                            .isNotNull(HyWmsCompleteDeliverOrder::getFinishDate)
                            .between(HyWmsCompleteDeliverOrder::getFinishDate, startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay())
                            .eq(HyWmsCompleteDeliverOrder::getState, CompleteDeliverOrderStateEnum.COMPLETED.getValue())
            );

            totalOrderCount = BigDecimal.valueOf(orders.size());
            totalOutput = orders.stream()
                    .map(order -> order.getActualQuantity() != null ? order.getActualQuantity() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        return PmPackageCompleteOutputResp.builder()
                .output(totalOutput)
                .orderNum(totalOrderCount)
                .dateStr(dateStr)
                .period(periodName)
                .build();
    }
}

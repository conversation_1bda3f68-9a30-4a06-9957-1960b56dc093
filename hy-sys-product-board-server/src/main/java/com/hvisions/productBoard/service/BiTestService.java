package com.hvisions.productBoard.service;

import com.alibaba.fastjson.JSONObject;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.productBoard.dto.ContractDTO;
import com.hvisions.productBoard.dto.LmContractDTO;
import com.hvisions.productBoard.dto.LmOriginDataDTO;
import com.hvisions.productBoard.entity.LmContract;
import com.hvisions.productBoard.entity.LmOriginData;
import com.hvisions.productBoard.entity.QmHoseAssemblyOrder;
import com.hvisions.productBoard.mapper.LmContractMapper;
import com.hvisions.productBoard.mapper.LmMapper;
import com.hvisions.productBoard.mapper.LmOriginDataMapper;
import com.hvisions.productBoard.mapper.QmHoseAssemblyOrderMapper;
import com.hvisions.productBoard.req.BiTestFinishedQuantityStatisticReq;
import com.hvisions.productBoard.resp.BiTestFinishedQuantityStatisticResp;
import com.hvisions.productBoard.resp.BiTestFinishedQuantityTodayAndCurrMonthResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产综合看板-五部试验中心看板
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class BiTestService {

    private final QmHoseAssemblyOrderMapper qmHoseAssemblyOrderMapper;

    private final LmMapper lmMapper;

    private final LmOriginDataMapper lmOriginDataMapper;
    private final LmContractMapper lmContractMapper;

    // ? 委托订单检测状态实时监控
    public List<LmContractDTO> getKeyOrderList() {
        List<LmOriginData> originDataList = lmOriginDataMapper.selectListOfStatusIng();

        if (CollectionUtils.isEmpty(originDataList)) {
            return new ArrayList<>();
        }

        Map<Long, List<LmOriginData>> contractIdMap = originDataList.stream()
                .filter(item -> Objects.nonNull(item.getContractId()))
                .collect(Collectors.groupingBy(LmOriginData::getContractId));

        if (MapUtils.isEmpty(contractIdMap)) {
            return new ArrayList<>();
        }

        List<LmContract> contractList = lmContractMapper.selectListByIdIn(contractIdMap.keySet());
        if (CollectionUtils.isEmpty(contractList)) {
            return new ArrayList<>();
        }

        return contractList.stream()
                .filter(item -> item.getContractTempletID() == 4) // 软管组件检验单
                .map(item -> {
                    List<LmOriginData> list = contractIdMap.get(item.getId());

                    if (CollectionUtils.isEmpty(list)) {
                        return null;
                    }

                    LmContractDTO dto = DtoMapper.convert(item, LmContractDTO.class);

                    dto.setOriginDataList(DtoMapper.convertList(list, LmOriginDataDTO.class));

                    return dto;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    // ? 试验任务完成情况
    public BiTestFinishedQuantityTodayAndCurrMonthResp getFinishedQuantityTodayAndCurrMonth1() {
        BiTestFinishedQuantityTodayAndCurrMonthResp resp = new BiTestFinishedQuantityTodayAndCurrMonthResp();

        LocalDate today = LocalDate.now();

        // 今日
        LocalDateTime startOfToday = today.atTime(0, 0, 0);
        LocalDateTime endOfToday = today.atTime(23, 59, 59);

        Long count1 = lmMapper.selectCountOfFinishedContractBetween(startOfToday, endOfToday);
        BigDecimal quantity1 = lmMapper.selectQuantityOfFinishedContractBetween(startOfToday, endOfToday);

        // 本月
        LocalDateTime startOfMonth = today.with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0);
        LocalDateTime endOfMonth = today.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);

        Long count2 = lmMapper.selectCountOfFinishedContractBetween(startOfMonth, endOfMonth);
        BigDecimal quantity2 = lmMapper.selectQuantityOfFinishedContractBetween(startOfMonth, endOfMonth);

        resp.setTodayFinishedCount(count1);
        resp.setTodayFinishedQuantity(quantity1);
        resp.setMonthFinishedCount(count2);
        resp.setMonthFinishedQuantity(quantity2);

        return resp;
    }

    // ? 耐压任务完成情况
    public BiTestFinishedQuantityTodayAndCurrMonthResp getFinishedQuantityTodayAndCurrMonth2() {
        BiTestFinishedQuantityTodayAndCurrMonthResp resp = new BiTestFinishedQuantityTodayAndCurrMonthResp();

        // 今日
        JSONObject json1 = getFinishedQuantityOfToday();

        resp.setTodayFinishedCount(json1.getLong("count"));
        resp.setTodayFinishedQuantity(json1.getBigDecimal("quantity"));

        // 本月
        JSONObject json2 = getFinishedQuantityOfCurrMonth();

        resp.setMonthFinishedCount(json2.getLong("count"));
        resp.setMonthFinishedQuantity(json2.getBigDecimal("quantity"));

        return resp;
    }

    public JSONObject getFinishedQuantityOfToday() {
        JSONObject json = new JSONObject();

        LocalDate today = LocalDate.now();

        long count = 0L;
        BigDecimal quantity = BigDecimal.ZERO;

        LocalDateTime start = today.atTime(0, 0, 0);
        LocalDateTime end = today.atTime(23, 59, 59);
        List<QmHoseAssemblyOrder> listOfToday = qmHoseAssemblyOrderMapper.selectListByTrialAccomplishTimeBetween(start, end);
        if (CollectionUtils.isNotEmpty(listOfToday)) {
            count = listOfToday.size();

            quantity = listOfToday.stream()
                    .map(QmHoseAssemblyOrder::getTrialPassCount)
                    .filter(Objects::nonNull)
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        json.put("count", count);
        json.put("quantity", quantity);

        return json;
    }

    public JSONObject getFinishedQuantityOfCurrMonth() {
        JSONObject json = new JSONObject();

        LocalDate today = LocalDate.now();

        long count = 0L;
        BigDecimal quantity = BigDecimal.ZERO;

        LocalDateTime start = today.with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0);
        LocalDateTime end = today.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);
        List<QmHoseAssemblyOrder> listOfToday = qmHoseAssemblyOrderMapper.selectListByTrialAccomplishTimeBetween(start, end);
        if (CollectionUtils.isNotEmpty(listOfToday)) {
            count = listOfToday.size();

            quantity = listOfToday.stream()
                    .map(QmHoseAssemblyOrder::getTrialPassCount)
                    .filter(Objects::nonNull)
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        json.put("count", count);
        json.put("quantity", quantity);

        return json;
    }

    // ? 试验完成统计
    public BiTestFinishedQuantityStatisticResp getFinishedQuantityStatistic1(BiTestFinishedQuantityStatisticReq req) {
        String mode = Optional.ofNullable(req.getMode())
                .orElseThrow(() -> new BaseKnownException("年月周字段为空"));

        BiTestFinishedQuantityStatisticResp resp = new BiTestFinishedQuantityStatisticResp();

        List<String> xAxisData;
        Map<String, Long> countMap;
        Map<String, BigDecimal> quantityMap;

        LocalDate today = LocalDate.now();

        // 年
        if (Objects.equals("YEAR", mode)) {
            LocalDate start = today.with(TemporalAdjusters.firstDayOfYear());
            LocalDate end = today.with(TemporalAdjusters.lastDayOfYear());

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

            List<LocalDate> dates = new ArrayList<>();

            int monthValue = today.getMonthValue();
            for (int i = 0; i < monthValue; i++) {
                dates.add(start.plusMonths(i));
            }

            xAxisData = dates.stream().map(formatter::format).collect(Collectors.toList());

            List<ContractDTO> list1 = lmMapper.selectListOfFinishedContractBetween2(start.atTime(0, 0, 0), end.atTime(23, 59, 59));
            if (CollectionUtils.isEmpty(list1)) {
                countMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> 0L));
            } else {
                Map<String, List<ContractDTO>> map = list1.stream()
                        .filter(i -> Objects.nonNull(i.getFinishDate()))
                        .collect(Collectors.groupingBy(i -> formatter.format(i.getFinishDate())));

                countMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return 0L;
                            }

                            return (long) map.get(i).size();
                        }));
            }

            List<ContractDTO> list2 = lmMapper.selectListOfFinishedContractBetween3(start.atTime(0, 0, 0), end.atTime(23, 59, 59));
            if (CollectionUtils.isEmpty(list2)) {
                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
            } else {
                Map<String, List<ContractDTO>> map = list1.stream()
                        .filter(i -> Objects.nonNull(i.getFinishDate()))
                        .collect(Collectors.groupingBy(i -> formatter.format(i.getFinishDate())));

                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(ContractDTO::getQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
            }
        }
        // 月
        else if (Objects.equals("MONTH", mode)) {
            LocalDate start = today.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate end = today.with(TemporalAdjusters.lastDayOfMonth());

            List<LocalDate> dates = new ArrayList<>();

            int endDayOfMonth = end.getDayOfMonth();
            for (int i = 0; i < endDayOfMonth; i++) {
                dates.add(start.plusDays(i));
            }

            int maxWeekOfMonth = dates.stream()
                    .map(date -> date.get(WeekFields.ISO.weekOfMonth()))
                    .max(Integer::compareTo)
                    .orElse(4);

            List<Integer> weekNums = new ArrayList<>();
            for (int i = 1; i <= maxWeekOfMonth; i++) {
                weekNums.add(i);
            }

            xAxisData = weekNums.stream()
                    .map(weekNum -> String.format("第%s周", weekNum))
                    .collect(Collectors.toList());

            List<ContractDTO> list1 = lmMapper.selectListOfFinishedContractBetween2(start.atTime(0, 0, 0), end.atTime(23, 59, 59));
            if (CollectionUtils.isEmpty(list1)) {
                countMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> 0L));
            } else {
                Map<Integer, List<ContractDTO>> map = list1.stream()
                        .filter(i -> Objects.nonNull(i.getFinishDate()))
                        .collect(Collectors.groupingBy(i -> i.getFinishDate().get(WeekFields.ISO.weekOfMonth())));

                countMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return 0L;
                            }

                            return (long) map.get(i).size();
                        }));
            }

            List<ContractDTO> list2 = lmMapper.selectListOfFinishedContractBetween3(start.atTime(0, 0, 0), end.atTime(23, 59, 59));
            if (CollectionUtils.isEmpty(list2)) {
                quantityMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> BigDecimal.ZERO));
            } else {
                Map<Integer, List<ContractDTO>> map = list1.stream()
                        .filter(i -> Objects.nonNull(i.getFinishDate()))
                        .collect(Collectors.groupingBy(i -> i.getFinishDate().get(WeekFields.ISO.weekOfMonth())));

                quantityMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(ContractDTO::getQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
            }
        }
        // 周
        else if (Objects.equals("WEEK", mode)) {
            LocalDate start = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            LocalDate end = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            List<LocalDate> dates = new ArrayList<>();

            while (!start.isAfter(end)) {
                dates.add(start);

                start = start.plusDays(1);
            }

            xAxisData = dates.stream().map(formatter::format).collect(Collectors.toList());

            List<ContractDTO> list1 = lmMapper.selectListOfFinishedContractBetween2(start.atTime(0, 0, 0), end.atTime(23, 59, 59));
            if (CollectionUtils.isEmpty(list1)) {
                countMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> 0L));
            } else {
                Map<String, List<ContractDTO>> map = list1.stream()
                        .filter(i -> Objects.nonNull(i.getFinishDate()))
                        .collect(Collectors.groupingBy(i -> formatter.format(i.getFinishDate())));

                countMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return 0L;
                            }

                            return (long) map.get(i).size();
                        }));
            }

            List<ContractDTO> list2 = lmMapper.selectListOfFinishedContractBetween3(start.atTime(0, 0, 0), end.atTime(23, 59, 59));
            if (CollectionUtils.isEmpty(list2)) {
                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
            } else {
                Map<String, List<ContractDTO>> map = list1.stream()
                        .filter(i -> Objects.nonNull(i.getFinishDate()))
                        .collect(Collectors.groupingBy(i -> formatter.format(i.getFinishDate())));

                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(ContractDTO::getQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
            }
        }
        // 其他
        else {
            throw new BaseKnownException("年月周字段错误");
        }

        resp.setXAxisData(xAxisData);
        resp.setCountMap(countMap);
        resp.setQuantityMap(quantityMap);

        return resp;
    }

    // ? 软管组件耐压完成情况
    // ? 软管组件检验单（新建、待复核 -> 进行中，试验完成、待质检、待审理、质检完成 -> 完成）
    public BiTestFinishedQuantityStatisticResp getFinishedQuantityStatistic2(BiTestFinishedQuantityStatisticReq req) {
        String mode = Optional.ofNullable(req.getMode())
                .orElseThrow(() -> new BaseKnownException("年月周字段为空"));

        BiTestFinishedQuantityStatisticResp resp = new BiTestFinishedQuantityStatisticResp();

        List<String> xAxisData;
        Map<String, Long> countMap;
        Map<String, BigDecimal> quantityMap;

        LocalDate today = LocalDate.now();

        // 年
        if (Objects.equals("YEAR", mode)) {
            LocalDate start = today.with(TemporalAdjusters.firstDayOfYear());
            LocalDate end = today.with(TemporalAdjusters.lastDayOfYear());

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

            List<LocalDate> dates = new ArrayList<>();

            int monthValue = today.getMonthValue();
            for (int i = 0; i < monthValue; i++) {
                dates.add(start.plusMonths(i));
            }

            xAxisData = dates.stream().map(formatter::format).collect(Collectors.toList());

            List<QmHoseAssemblyOrder> list = qmHoseAssemblyOrderMapper.selectListByTrialAccomplishTimeBetween(
                    start.atTime(0, 0, 0),
                    end.atTime(23, 59, 59)
            );
            if (CollectionUtils.isEmpty(list)) {
                countMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> 0L));
                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
            } else {
                Map<String, List<QmHoseAssemblyOrder>> map = list.stream()
                        .filter(i -> Objects.nonNull(i.getTrialAccomplishTime()))
                        .collect(Collectors.groupingBy(i -> formatter.format(i.getTrialAccomplishTime())));

                countMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return 0L;
                            }

                            return (long) map.get(i).size();
                        }));

                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(QmHoseAssemblyOrder::getTrialPassCount)
                                    .filter(Objects::nonNull)
                                    .map(BigDecimal::valueOf)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
            }
        }
        // 月
        else if (Objects.equals("MONTH", mode)) {
            LocalDate start = today.with(TemporalAdjusters.firstDayOfMonth());
            LocalDate end = today.with(TemporalAdjusters.lastDayOfMonth());

            List<LocalDate> dates = new ArrayList<>();

            int endDayOfMonth = end.getDayOfMonth();
            for (int i = 0; i < endDayOfMonth; i++) {
                dates.add(start.plusDays(i));
            }

            int maxWeekOfMonth = dates.stream()
                    .map(date -> date.get(WeekFields.ISO.weekOfMonth()))
                    .max(Integer::compareTo)
                    .orElse(4);

            List<Integer> weekNums = new ArrayList<>();
            for (int i = 1; i <= maxWeekOfMonth; i++) {
                weekNums.add(i);
            }

            xAxisData = weekNums.stream()
                    .map(weekNum -> String.format("第%s周", weekNum))
                    .collect(Collectors.toList());

            List<QmHoseAssemblyOrder> list = qmHoseAssemblyOrderMapper.selectListByTrialAccomplishTimeBetween(
                    start.atTime(0, 0, 0),
                    end.atTime(23, 59, 59)
            );
            if (CollectionUtils.isEmpty(list)) {
                countMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> 0L));
                quantityMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> BigDecimal.ZERO));
            } else {
                Map<Integer, List<QmHoseAssemblyOrder>> map = list.stream()
                        .filter(i -> Objects.nonNull(i.getTrialAccomplishTime()))
                        .collect(Collectors.groupingBy(i -> i.getTrialAccomplishTime().get(WeekFields.ISO.weekOfMonth())));

                countMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return 0L;
                            }

                            return (long) map.get(i).size();
                        }));

                quantityMap = weekNums.stream()
                        .collect(Collectors.toMap(i -> String.format("第%d周", i), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(QmHoseAssemblyOrder::getTrialPassCount)
                                    .filter(Objects::nonNull)
                                    .map(BigDecimal::valueOf)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
            }
        }
        // 周
        else if (Objects.equals("WEEK", mode)) {
            LocalDate start = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            LocalDate end = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            List<LocalDate> dates = new ArrayList<>();

            while (!start.isAfter(end)) {
                dates.add(start);

                start = start.plusDays(1);
            }

            xAxisData = dates.stream().map(formatter::format).collect(Collectors.toList());

            List<QmHoseAssemblyOrder> list = qmHoseAssemblyOrderMapper.selectListByTrialAccomplishTimeBetween(
                    start.atTime(0, 0, 0),
                    end.atTime(23, 59, 59)
            );
            if (CollectionUtils.isEmpty(list)) {
                countMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> 0L));
                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
            } else {
                Map<String, List<QmHoseAssemblyOrder>> map = list.stream()
                        .filter(i -> Objects.nonNull(i.getTrialAccomplishTime()))
                        .collect(Collectors.groupingBy(i -> formatter.format(i.getTrialAccomplishTime())));

                countMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return 0L;
                            }

                            return (long) map.get(i).size();
                        }));

                quantityMap = xAxisData.stream()
                        .collect(Collectors.toMap(Function.identity(), i -> {
                            if (CollectionUtils.isEmpty(map.get(i))) {
                                return BigDecimal.ZERO;
                            }

                            return map.get(i).stream()
                                    .map(QmHoseAssemblyOrder::getTrialPassCount)
                                    .filter(Objects::nonNull)
                                    .map(BigDecimal::valueOf)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        }));
            }
        }
        // 其他
        else {
            throw new BaseKnownException("年月周字段错误");
        }

        resp.setXAxisData(xAxisData);
        resp.setCountMap(countMap);
        resp.setQuantityMap(quantityMap);

        return resp;
    }

    // ? 本月返厂件完成情况

    // ? 通知列表
}

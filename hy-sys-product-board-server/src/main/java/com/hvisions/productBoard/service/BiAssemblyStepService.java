package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.productBoard.entity.*;
import com.hvisions.productBoard.mapper.*;
import com.hvisions.productBoard.req.BiAssemblyStepFinishedQuantityStatisticReq;
import com.hvisions.productBoard.resp.BiAssemblyStepFinishedQuantityStatisticResp;
import com.hvisions.productBoard.resp.BiAssemblyStepKeyOrderResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 生产综合看板 五部工位看板
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class BiAssemblyStepService {

    private final BmMaterialMapper bmMaterialMapper;

    private final BmBomMapper bmBomMapper;
    private final BmBomItemMapper bmBomItemMapper;

    private final PmAssemblyWorkOrderMapper pmAssemblyWorkOrderMapper;
    private final PmAssemblyWorkSubOrderMapper pmAssemblyWorkSubOrderMapper;
    private final PmAssemblyWorkSubOrderOperationOutputMapper pmAssemblyWorkSubOrderOperationOutputMapper;

    private final WmDeliverOrderLineMapper wmDeliverOrderLineMapper;

    private final SaSaleOrderMapper saSaleOrderMapper;
    private final SaSaleOrderLineMapper saSaleOrderLineMapper;

    private final QmHoseAssemblyOrderMapper qmHoseAssemblyOrderMapper;
    private final QmHoseAssemblyOrderLineMapper qmHoseAssemblyOrderLineMapper;

    /**
     * 工艺规程书【需要先完成MES改动】
     * 用户按照工位上传
     */
    public void getSpecificationFile() {
    }

    /**
     * 当班人员【需要先完成MES改动】
     * 显示当前工位人员，MES 中维护工位人员，人员在岗状态业主手动在MES中调整
     */
    public void getTeamPerson() {

    }

    /**
     * 关键任务清单
     * 紧急字段：原始记录单是否紧急字段
     * 原料准备字段：已领金属件数量 / 总金属件数量
     * 排序：状态字段（紧急 > 常规）原料准备字段 倒序
     */
    public List<BiAssemblyStepKeyOrderResp> getKeyOrderList() {
        List<BiAssemblyStepKeyOrderResp> list = new ArrayList<>();

        List<PmAssemblyWorkSubOrder> pmAssemblyWorkSubOrders = pmAssemblyWorkSubOrderMapper.selectList(Wrappers.lambdaQuery(PmAssemblyWorkSubOrder.class)
                .orderByDesc(PmAssemblyWorkSubOrder::getIsUrgency)
                .last("limit 10")
        );
        if (CollectionUtils.isEmpty(pmAssemblyWorkSubOrders)) {
            return list;
        }

        Map<String, BigDecimal> workOrderCodeMap = new HashMap<>();

        pmAssemblyWorkSubOrders.stream()
                .map(PmAssemblyWorkSubOrder::getOrderCode)
                .distinct()
                .forEach(workOrderCode -> {
                    BigDecimal rawMaterialPercent = BigDecimal.ZERO;

                    try {
                        PmAssemblyWorkOrder workOrder = pmAssemblyWorkOrderMapper.selectOne(
                                Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                                        .eq(PmAssemblyWorkOrder::getCode, workOrderCode)
                        );

                        BigDecimal rawMaterialPrepareQuantity = BigDecimal.ZERO;
                        BigDecimal rawMaterialTotalQuantity = BigDecimal.ZERO;

                        try {
                            String deliverOrderCode = Optional.ofNullable(workOrder.getDeliverOrderCode())
                                    .orElseThrow(() -> new BaseKnownException("领料单号为空"));

                            List<WmDeliverOrderLine> deliverOrderLines = wmDeliverOrderLineMapper.selectList(Wrappers.lambdaQuery(WmDeliverOrderLine.class)
                                    .eq(WmDeliverOrderLine::getDeliverOrderCode, deliverOrderCode)
                            );
                            if (CollectionUtils.isEmpty(deliverOrderLines)) {
                                throw new BaseKnownException("领料单明细为空");
                            }

                            rawMaterialPrepareQuantity = deliverOrderLines.stream()
                                    .filter(line -> Objects.equals("JSJ", line.getMaterialGroup()))
                                    .map(WmDeliverOrderLine::getTargetQuantity)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                        } catch (Exception ignore) {
                        }

                        try {
                            BigDecimal planQuantity = Optional.ofNullable(workOrder.getPlanQuantity())
                                    .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                                    .orElseThrow(() -> new BaseKnownException("生产任务单计划数量为空"));

                            Integer bomId = Optional.ofNullable(workOrder.getBomId())
                                    .orElseThrow(() -> new BaseKnownException("BOM ID 为空"));

                            BmBom bom = bmBomMapper.selectOne(Wrappers.lambdaQuery(BmBom.class).eq(BmBom::getId, bomId));
                            if (Objects.isNull(bom)) {
                                throw new BaseKnownException("BOM 为不存在");
                            }

                            BigDecimal bomQuantity = Optional.ofNullable(bom.getBomCount())
                                    .filter(qty -> qty.compareTo(BigDecimal.ZERO) > 0)
                                    .orElseThrow(() -> new BaseKnownException("BOM 数量为空"));

                            List<BmBomItem> bomItems = bmBomItemMapper.selectList(Wrappers.lambdaQuery(BmBomItem.class).eq(BmBomItem::getBomId, bomId));
                            if (CollectionUtils.isEmpty(bomItems)) {
                                throw new BaseKnownException("BOM 子项为不存在");
                            }

                            Set<Integer> materialIds = bomItems.stream()
                                    .map(BmBomItem::getMaterialsId)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toSet());
                            if (CollectionUtils.isEmpty(materialIds)) {
                                throw new BaseKnownException("BOM 子项对应的物料ID不存在");
                            }

                            List<BmMaterial> materials = bmMaterialMapper.selectList(Wrappers.lambdaQuery(BmMaterial.class).in(BmMaterial::getId, materialIds));
                            if (CollectionUtils.isEmpty(materials)) {
                                throw new BaseKnownException("BOM 子项对应的物料不存在");
                            }

                            List<BmBomItem> jsjBomItems = bomItems.stream()
                                    .filter(bomItem -> {
                                        boolean flag;

                                        try {
                                            BmMaterial material = materials.stream()
                                                    .filter(ma -> Objects.equals(ma.getId(), bomItem.getMaterialsId()))
                                                    .findFirst()
                                                    .orElseThrow(() -> new BaseKnownException("BOM 子项对应的物料不存在"));

                                            flag = Objects.equals("JSJ", material.getMaterialGroupCode());
                                        } catch (Exception ignore) {
                                            flag = false;
                                        }

                                        return flag;
                                    })
                                    .collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(jsjBomItems)) {
                                throw new BaseKnownException("BOM子项中没有金属件");
                            }

                            BigDecimal bomJsjQuantity = jsjBomItems.stream()
                                    .map(BmBomItem::getBomItemCount)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                            rawMaterialTotalQuantity = planQuantity.multiply(bomJsjQuantity).divide(bomQuantity, 3, RoundingMode.HALF_UP);
                        } catch (Exception ignore) {
                        }

                        if (rawMaterialPrepareQuantity.compareTo(BigDecimal.ZERO) > 0 && rawMaterialTotalQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            rawMaterialPercent = new BigDecimal("100").multiply(rawMaterialPrepareQuantity).divide(rawMaterialTotalQuantity, 2, RoundingMode.HALF_UP);
                        }
                    } catch (Exception ignore) {
                    }

                    workOrderCodeMap.put(workOrderCode, rawMaterialPercent);
                });

        pmAssemblyWorkSubOrders.forEach(pmAssemblyWorkSubOrder -> {
            BiAssemblyStepKeyOrderResp resp = new BiAssemblyStepKeyOrderResp();

            resp.setCode(pmAssemblyWorkSubOrder.getCode());
            resp.setWorkOrderCode(pmAssemblyWorkSubOrder.getOrderCode());
            resp.setSaleOrderContractCode(pmAssemblyWorkSubOrder.getSaleOrderContractCode());
            resp.setProductModel(pmAssemblyWorkSubOrder.getProductModel());
            resp.setProductEigenvalue(pmAssemblyWorkSubOrder.getEigenvalue());
            resp.setPlanQuantity(pmAssemblyWorkSubOrder.getPlanQuantity());

            resp.setStatusName(Objects.equals(pmAssemblyWorkSubOrder.getIsUrgency(), true) ? "紧急" : "常规");

            BigDecimal rawMaterialPercent = BigDecimal.ZERO;

            try {
                String workOrderCode = Optional.ofNullable(pmAssemblyWorkSubOrder.getOrderCode())
                        .orElseThrow(() -> new BaseKnownException("生产任务单为空"));

                rawMaterialPercent = Optional.ofNullable(workOrderCodeMap.get(workOrderCode)).orElse(BigDecimal.ZERO);
            } catch (Exception ignore) {
            }

            resp.setRawMaterialPercent(rawMaterialPercent);

            list.add(resp);
        });

        return list;
    }

    /**
     * 近七日任务完成数统计
     */
    public BiAssemblyStepFinishedQuantityStatisticResp getFinishQuantityStatisticOfLast7Days(BiAssemblyStepFinishedQuantityStatisticReq req) {
        String stepName = Optional.ofNullable(req.getStepName())
                .orElseThrow(() -> new BaseKnownException("工序名称为空"));

        BiAssemblyStepFinishedQuantityStatisticResp resp = new BiAssemblyStepFinishedQuantityStatisticResp();

        List<String> xAxisData;
        Map<String, BigDecimal> quantityMap;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.now();

        List<LocalDate> dateList = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            dateList.add(today.minusDays(6 - i));
        }

        xAxisData = dateList.stream().map(formatter::format).collect(Collectors.toList());

        List<PmAssemblyWorkSubOrderOperationOutput> list = pmAssemblyWorkSubOrderOperationOutputMapper.selectListByCreateTimeBetween(
                today.minusDays(6).atTime(0, 0, 0),
                today.atTime(23, 59, 59)
        );
        if (CollectionUtils.isEmpty(list)) {
            quantityMap = xAxisData.stream()
                    .collect(Collectors.toMap(Function.identity(), i -> BigDecimal.ZERO));
        } else {
            Map<String, List<PmAssemblyWorkSubOrderOperationOutput>> map = list.stream()
                    .filter(item -> Objects.equals(item.getOperationName(), stepName))
                    .filter(item -> Objects.nonNull(item.getCreateTime()))
                    .collect(Collectors.groupingBy(item ->
                            formatter.format(item.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    ));

            quantityMap = xAxisData.stream()
                    .collect(Collectors.toMap(Function.identity(), i -> {
                        if (CollectionUtils.isEmpty(map.get(i))) {
                            return BigDecimal.ZERO;
                        }

                        return map.get(i).stream()
                                .map(PmAssemblyWorkSubOrderOperationOutput::getCount)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }));
        }

        resp.setXAxisData(xAxisData);
        resp.setQuantityMap(quantityMap);

        return resp;
    }

    // ? 通知列表

}

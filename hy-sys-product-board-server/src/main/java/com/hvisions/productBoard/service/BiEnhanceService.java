package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.productBoard.entity.HyPmEnhanceWorkOrder;
import com.hvisions.productBoard.enums.EnhanceWorkOrderStateEnum;
import com.hvisions.productBoard.mapper.HyPmEnhanceWorkOrderMapper;
import com.hvisions.productBoard.resp.BiEnhanceDataOverviewResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiEnhanceService {

    @Resource
    private HyPmEnhanceWorkOrderMapper hyPmEnhanceWorkOrderMapper;

    public String getSafeDays() {
        return null;
    }

    public BiEnhanceDataOverviewResp getDataOverview() {

        YearMonth currentMonth = YearMonth.now();
        LocalDate startDate = currentMonth.atDay(1);
        LocalDate endDate = currentMonth.atEndOfMonth();

        List<HyPmEnhanceWorkOrder> orders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .between(HyPmEnhanceWorkOrder::getPlanEndTime, startDate.atStartOfDay(), endDate.atTime(LocalTime.MAX))
        );

        int totalOrderCount = orders.size();
        BigDecimal totalOrderQuantity = orders.stream()
                .map(HyPmEnhanceWorkOrder::getPlanQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<HyPmEnhanceWorkOrder> finishedOrders = orders.stream()
                .filter(order -> EnhanceWorkOrderStateEnum.PRODUCED.getValue().equals(order.getState()))
                .collect(Collectors.toList());

        int finishedOrderCount = finishedOrders.size();
        BigDecimal finishedOrderQuantity = finishedOrders.stream()
                .map(HyPmEnhanceWorkOrder::getFinishedQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return BiEnhanceDataOverviewResp.builder()
                .totalOrderCount(totalOrderCount)
                .totalOrderQuantity(totalOrderQuantity)
                .finishedOrderCount(finishedOrderCount)
                .finishedOrderQuantity(finishedOrderQuantity)
                .build();
    }

    public String getKeyMaterials() {
        return null;
    }

    public String getKeyOrder() {
        return null;
    }

    public String getKeyWorkOrderCompleteOutput() {
        return null;
    }

    public String getNotice() {
        return null;
    }
}

package com.hvisions.productBoard.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.productBoard.entity.HyPmMoldingBlankWorkOrder;
import com.hvisions.productBoard.entity.HyPmMoldingWorkOrder;
import com.hvisions.productBoard.enums.MoldingTaskTypeEnum;
import com.hvisions.productBoard.enums.MoldingTestTaskStateEnum;
import com.hvisions.productBoard.enums.MoldingWorkOrderStateEnum;
import com.hvisions.productBoard.mapper.HyPmMoldingBlankWorkOrderMapper;
import com.hvisions.productBoard.mapper.HyPmMoldingWorkOrderMapper;
import com.hvisions.productBoard.resp.BiMoldingDataOverviewResp;
import com.hvisions.productBoard.resp.BiMoldingKeyOrderResp;
import com.hvisions.productBoard.resp.BiMoldingKeyOrderStepResp;
import com.hvisions.productBoard.resp.BiMoldingTestTaskResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiMoldingService {

    @Resource
    private HyPmMoldingWorkOrderMapper hyPmMoldingWorkOrderMapper;
    @Resource
    private HyPmMoldingBlankWorkOrderMapper hyPmMoldingBlankWorkOrderMapper;

    public String getSafeDays() {
        return null;
    }

    public BiMoldingDataOverviewResp getDataOverview() {

        YearMonth currentMonth = YearMonth.now();
        LocalDate startDate = currentMonth.atDay(1);
        LocalDate endDate = currentMonth.atEndOfMonth();

        List<HyPmMoldingWorkOrder> orders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .between(HyPmMoldingWorkOrder::getPlanEndTime, startDate.atStartOfDay(), endDate.atTime(LocalTime.MAX))
        );

        int totalOrderCount = orders.size();
        BigDecimal totalOrderQuantity = orders.stream()
                .map(HyPmMoldingWorkOrder::getPlanQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<HyPmMoldingWorkOrder> finishedOrders = orders.stream()
                .filter(order -> MoldingWorkOrderStateEnum.PRODUCED_FINISH.getValue().equals(order.getState()))
                .collect(Collectors.toList());

        int finishedOrderCount = finishedOrders.size();
        BigDecimal finishedOrderQuantity = finishedOrders.stream()
                .map(HyPmMoldingWorkOrder::getFinishedQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return BiMoldingDataOverviewResp.builder()
                .totalOrderCount(totalOrderCount)
                .totalOrderQuantity(totalOrderQuantity)
                .finishedOrderCount(finishedOrderCount)
                .finishedOrderQuantity(finishedOrderQuantity)
                .build();
    }

    public List<BiMoldingKeyOrderResp> getKeyOrder() {

        QueryWrapper<HyPmMoldingWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(HyPmMoldingWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCING.getValue())
                .orderByAsc(HyPmMoldingWorkOrder::getPlanStartTime)
                .last("LIMIT 5");

        List<HyPmMoldingWorkOrder> orders = hyPmMoldingWorkOrderMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(orders)) {
            return Collections.emptyList();
        }

        List<BiMoldingKeyOrderResp> result = new ArrayList<>();

        for (HyPmMoldingWorkOrder order : orders) {

            QueryWrapper<HyPmMoldingBlankWorkOrder> blankQueryWrapper = new QueryWrapper<>();
            blankQueryWrapper.lambda()
                    .eq(HyPmMoldingBlankWorkOrder::getWorkOrderCode, order.getCode())
                    .orderByAsc(HyPmMoldingBlankWorkOrder::getType);

            List<HyPmMoldingBlankWorkOrder> blankOrders = hyPmMoldingBlankWorkOrderMapper.selectList(blankQueryWrapper);

            List<BiMoldingKeyOrderStepResp> steps = blankOrders.stream()
                    .map(blankOrder -> {
                        BigDecimal finishedQuantity = blankOrder.getActualQuantity() != null ? blankOrder.getActualQuantity() : BigDecimal.ZERO;
                        BigDecimal planQuantity = blankOrder.getPlanQuantity() != null ? blankOrder.getPlanQuantity() : BigDecimal.ZERO;

                        // 计算完成率
                        double percentage = 0.0;
                        if (planQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            percentage = finishedQuantity.divide(planQuantity, 4, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100)).doubleValue();
                        }

                        // TODO 如何区分今天的完成数量
                        BigDecimal todayFinishedQuantity = finishedQuantity;
                        BigDecimal todayPlanQuantity = planQuantity;

                        return BiMoldingKeyOrderStepResp.builder()
                                .stepName(MoldingTaskTypeEnum.getNameByValue(blankOrder.getType()))
                                .finishedQuantity(finishedQuantity)
                                .planQuantity(planQuantity)
                                .percentage(percentage)
                                .todayFinishedQuantity(todayFinishedQuantity)
                                .todayPlanQuantity(todayPlanQuantity)
                                .unit(blankOrder.getUnitSymbol())
                                .build();
                    })
                    .collect(Collectors.toList());

            BiMoldingKeyOrderResp resp = BiMoldingKeyOrderResp.builder()
                    .orderCode(order.getCode())
                    .productType(order.getProductName())
                    .steps(steps)
                    .build();

            result.add(resp);
        }

        return result;
    }

    public String getKeyMaterials() {
        return null;
    }

    public List<BiMoldingTestTaskResp> getTestWorkOrder() {

        QueryWrapper<HyPmMoldingBlankWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HyPmMoldingBlankWorkOrder::getTrialState, MoldingTestTaskStateEnum.PRODUCING.getValue())
                .orderByAsc(HyPmMoldingBlankWorkOrder::getActualStartDateTime);
        List<HyPmMoldingBlankWorkOrder> orders = hyPmMoldingBlankWorkOrderMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(orders)) {
            return Collections.emptyList();
        }

        return orders.stream().map(order -> BiMoldingTestTaskResp.builder()
                .code(order.getCode())
                .batchNo(order.getProductBatchNumber())
                .eigenvalue(order.getProductEigenvalue())
                .stepName(MoldingTaskTypeEnum.getNameByValue(order.getType()))
                .startTime(order.getActualStartDateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                .build()).collect(Collectors.toList());
    }

    public String getNotice() {
        return null;
    }
}

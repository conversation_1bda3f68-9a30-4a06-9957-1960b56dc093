package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.productBoard.entity.HyPmPkgOrder;
import com.hvisions.productBoard.entity.HyPmPkgOrderLine;
import com.hvisions.productBoard.entity.HyWmsCompleteDeliverOrder;
import com.hvisions.productBoard.entity.HyWmsCompleteDeliverOrderLine;
import com.hvisions.productBoard.enums.CompleteDeliverOrderStateEnum;
import com.hvisions.productBoard.enums.PackageWorkOrderStateEnum;
import com.hvisions.productBoard.mapper.HyPmPkgOrderLineMapper;
import com.hvisions.productBoard.mapper.HyPmPkgOrderMapper;
import com.hvisions.productBoard.mapper.HyWmsCompleteDeliverOrderLineMapper;
import com.hvisions.productBoard.mapper.HyWmsCompleteDeliverOrderMapper;
import com.hvisions.productBoard.req.CommonPageReq;
import com.hvisions.productBoard.resp.*;
import com.hvisions.productBoard.util.PageHelperAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BiPackageService {

    @Resource
    private HyPmPkgOrderMapper hyPmPkgOrderMapper;

    @Resource
    private HyWmsCompleteDeliverOrderMapper hyWmsCompleteDeliverOrderMapper;

    @Resource
    private HyPmPkgOrderLineMapper hyPmPkgOrderLineMapper;

    @Resource
    private HyWmsCompleteDeliverOrderLineMapper hyWmsCompleteDeliverOrderLineMapper;

    public BiPackageDataOverviewResp getDataOverview() {

        QueryWrapper<HyPmPkgOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HyPmPkgOrder::getState, PackageWorkOrderStateEnum.NEW.getValue());
        List<HyPmPkgOrder> orders = hyPmPkgOrderMapper.selectList(queryWrapper);

        Integer taskCount = orders.size();
        BigDecimal taskQuantity = orders.stream()
                .map(HyPmPkgOrder::getQuantity)
                .map(e -> e == null ? BigDecimal.ZERO : new BigDecimal(e))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return BiPackageDataOverviewResp.builder()
                .taskCount(taskCount)
                .taskQuantity(taskQuantity)
                .build();
    }

    public List<BiPackageOnDutyResp> getOnDuty() {
        return Collections.emptyList();
    }

    public Page<BiPackageOrderResp> getPackageOrder(Integer current, Integer size) {

        QueryWrapper<HyPmPkgOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.select("code", "contract_num");
        orderQueryWrapper.lambda()
                .eq(HyPmPkgOrder::getState, PackageWorkOrderStateEnum.NEW.getValue());

        List<HyPmPkgOrder> orders = hyPmPkgOrderMapper.selectList(orderQueryWrapper);
        List<String> codes = orders.stream()
                .map(HyPmPkgOrder::getCode)
                .collect(Collectors.toList());

        Map<String, String> contractMap = orders.stream()
                .collect(Collectors.toMap(HyPmPkgOrder::getCode, HyPmPkgOrder::getContractNum));

        if (!codes.isEmpty()) {
            CommonPageReq pageReq = PageHelperAdapter.createPageReq(current, size);

            org.springframework.data.domain.Page<HyPmPkgOrderLine> page = PageHelperUtil.getPage(() -> {
                QueryWrapper<HyPmPkgOrderLine> lineQueryWrapper = new QueryWrapper<>();
                lineQueryWrapper.lambda()
                        .in(HyPmPkgOrderLine::getCode, codes)
                        .orderByDesc(HyPmPkgOrderLine::getCreateTime);
                return hyPmPkgOrderLineMapper.selectList(lineQueryWrapper);
            }, pageReq);

            List<BiPackageOrderResp> list = page.getContent().stream()
                    .map(line -> BiPackageOrderResp.builder()
                            .contractCode(contractMap.get(line.getCode()))
                            .submitOrderCode(line.getSubmitOrderCode())
                            .productModel(line.getProductModel())
                            .productSpec(line.getMaterialEigenvalue())
                            .quantity(line.getQuantity())
                            .build())
                    .collect(Collectors.toList());

            Page<BiPackageOrderResp> resultPage = new Page<>(current, size);
            resultPage.setTotal(page.getTotalElements());
            resultPage.setPages((long) page.getTotalPages());
            resultPage.setRecords(list);
            return resultPage;
        }

        return new Page<>(current, size, 0);
    }

    public Page<BiWarehouseOrderResp> getWarehouseOrder(Integer current, Integer size) {

        QueryWrapper<HyWmsCompleteDeliverOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.select("code");
        orderQueryWrapper.lambda()
                .eq(HyWmsCompleteDeliverOrder::getState, CompleteDeliverOrderStateEnum.NEW.getValue());

        List<HyWmsCompleteDeliverOrder> orders = hyWmsCompleteDeliverOrderMapper.selectList(orderQueryWrapper);
        List<String> codes = orders.stream()
                .map(HyWmsCompleteDeliverOrder::getCode)
                .collect(Collectors.toList());

        if (!codes.isEmpty()) {
            CommonPageReq pageReq = PageHelperAdapter.createPageReq(current, size);

            org.springframework.data.domain.Page<HyWmsCompleteDeliverOrderLine> page = PageHelperUtil.getPage(() -> {
                QueryWrapper<HyWmsCompleteDeliverOrderLine> lineQueryWrapper = new QueryWrapper<>();
                lineQueryWrapper.lambda()
                        .in(HyWmsCompleteDeliverOrderLine::getCode, codes)
                        .orderByDesc(HyWmsCompleteDeliverOrderLine::getCreateTime);
                return hyWmsCompleteDeliverOrderLineMapper.selectList(lineQueryWrapper);
            }, pageReq);

            List<String> pkgOrderCodes = page.getContent().stream()
                    .map(HyWmsCompleteDeliverOrderLine::getPkgOrderCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            Map<String, String> contractMap = new HashMap<>();
            if (!pkgOrderCodes.isEmpty()) {
                QueryWrapper<HyPmPkgOrder> pkgOrderQueryWrapper = new QueryWrapper<>();
                pkgOrderQueryWrapper.select("code", "contract_num");
                pkgOrderQueryWrapper.lambda()
                        .in(HyPmPkgOrder::getCode, pkgOrderCodes);

                List<HyPmPkgOrder> pkgOrders = hyPmPkgOrderMapper.selectList(pkgOrderQueryWrapper);
                contractMap = pkgOrders.stream()
                        .collect(Collectors.toMap(HyPmPkgOrder::getCode, HyPmPkgOrder::getContractNum));
            }

            Map<String, String> finalContractMap = contractMap;
            List<BiWarehouseOrderResp> list = page.getContent().stream()
                    .map(line -> BiWarehouseOrderResp.builder()
                            .contractCode(finalContractMap.getOrDefault(line.getPkgOrderCode(), ""))
                            .submitOrderCode(line.getSaleOrderCode())
                            .productModel(line.getProductModel())
                            .productSpec(line.getMaterialEigenvalue())
                            .quantity(line.getQuantity())
                            .build())
                    .collect(Collectors.toList());

            Page<BiWarehouseOrderResp> resultPage = new Page<>(current, size);
            resultPage.setTotal(page.getTotalElements());
            resultPage.setPages((long) page.getTotalPages());
            resultPage.setRecords(list);
            return resultPage;
        }

        return new Page<>(current, size, 0);
    }

    public List<BiPackageCompleteOutputResp> getCompleteOutput() {

        QueryWrapper<HyPmPkgOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(HyPmPkgOrder::getState, PackageWorkOrderStateEnum.PACKAGED.getValue())
                .isNotNull(HyPmPkgOrder::getPkgDate)
                .ge(HyPmPkgOrder::getPkgDate, LocalDate.now().withDayOfYear(1).atStartOfDay());

        List<HyPmPkgOrder> orders = hyPmPkgOrderMapper.selectList(queryWrapper);

        String[] monthMaps = {"一月", "二月", "三月", "四月", "五月", "六月",
                "七月", "八月", "九月", "十月", "十一月", "十二月"};

        return orders.stream()
                .collect(Collectors.groupingBy(order ->
                        order.getPkgDate().toInstant().atZone(java.time.ZoneId.systemDefault()).getMonth().getValue()))
                .entrySet().stream()
                .map(entry -> {
                    int monthValue = entry.getKey();
                    String monthName = monthMaps[monthValue - 1];
                    Integer orderCount = entry.getValue().size();
                    BigDecimal productCount = entry.getValue().stream()
                            .map(HyPmPkgOrder::getQuantity)
                            .map(e -> e == null ? BigDecimal.ZERO : new BigDecimal(e))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    return BiPackageCompleteOutputResp.builder()
                            .dateStr(monthName)
                            .orderCount(orderCount)
                            .quantity(productCount)
                            .build();
                })
                .collect(Collectors.toList());
    }

    public String getNotice() {
        return "";
    }
}

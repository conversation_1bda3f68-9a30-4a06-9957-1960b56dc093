package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.productBoard.entity.HyQmHoseAssemblyOrder;
import com.hvisions.productBoard.entity.HyQmProductCheckSubmit;
import com.hvisions.productBoard.enums.HoseInspectionStateEnum;
import com.hvisions.productBoard.enums.InspectionTypeEnum;
import com.hvisions.productBoard.enums.ProductCheckSubmitStateEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.HyQmHoseAssemblyOrderMapper;
import com.hvisions.productBoard.mapper.HyQmProductCheckSubmitMapper;
import com.hvisions.productBoard.resp.PmInspectionCompleteOutputResp;
import com.hvisions.productBoard.resp.PmInspectionDataOverviewResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

@Slf4j
@Service
@RequiredArgsConstructor
public class PmInspectionService {

    @Resource
    private HyQmHoseAssemblyOrderMapper hyQmHoseAssemblyOrderMapper;
    @Resource
    private HyQmProductCheckSubmitMapper hyQmProductCheckSubmitMapper;

    public PmInspectionDataOverviewResp getDataOverview() {

        // 状态为待质检(PENDING_QA=15)、待审理(PENDING_TRAIL=20)的未完成订单
        List<HyQmHoseAssemblyOrder> hoseOrders = hyQmHoseAssemblyOrderMapper.selectList(
                Wrappers.lambdaQuery(HyQmHoseAssemblyOrder.class)
                        .in(HyQmHoseAssemblyOrder::getQualityState,
                                HoseInspectionStateEnum.PENDING_QA.getValue(),
                                HoseInspectionStateEnum.PENDING_TRAIL.getValue())
        );

        Integer orderCount = hoseOrders.size();
        BigDecimal orderQuantity = hoseOrders.stream()
                .map(order -> {
                    Integer totalCount = order.getTotalCount() != null ? order.getTotalCount() : 0;
                    Integer qualityPassCount = order.getQualityPassCount() != null ? order.getQualityPassCount() : 0;
                    return BigDecimal.valueOf(totalCount - qualityPassCount);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 状态为待军检(PENDING_MILITARY=30)、待质检(PENDING_QUALITY=0)、待打印合格证(PENDING_PRINT=35)的未完成订单
        List<HyQmProductCheckSubmit> productCheckOrders = hyQmProductCheckSubmitMapper.selectList(
                Wrappers.lambdaQuery(HyQmProductCheckSubmit.class)
                        .in(HyQmProductCheckSubmit::getQualityState,
                                ProductCheckSubmitStateEnum.PENDING_MILITARY.getValue(),
                                ProductCheckSubmitStateEnum.PENDING_QUALITY.getValue(),
                                ProductCheckSubmitStateEnum.PENDING_PRINT.getValue())
        );

        Integer nearOrderCount = productCheckOrders.size();
        BigDecimal nearOrderQuantity = productCheckOrders.stream()
                .map(order -> {
                    Integer inspectionQuantity = order.getInspectionQuantity() != null ? order.getInspectionQuantity() : 0;
                    Integer qualifiedQuantity = order.getQualifiedQuantity() != null ? order.getQualifiedQuantity() : 0;
                    return BigDecimal.valueOf(inspectionQuantity - qualifiedQuantity);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<HyQmHoseAssemblyOrder> urgentOrders = hyQmHoseAssemblyOrderMapper.selectList(
                Wrappers.lambdaQuery(HyQmHoseAssemblyOrder.class)
                        .eq(HyQmHoseAssemblyOrder::getIsUrgency, 1)
                        .in(HyQmHoseAssemblyOrder::getQualityState,
                                HoseInspectionStateEnum.PENDING_QA.getValue(),
                                HoseInspectionStateEnum.PENDING_TRAIL.getValue())
        );

        Integer errorOrderCount = urgentOrders.size();
        BigDecimal errorOrderQuantity = urgentOrders.stream()
                .map(order -> {
                    Integer totalCount = order.getTotalCount() != null ? order.getTotalCount() : 0;
                    Integer qualityPassCount = order.getQualityPassCount() != null ? order.getQualityPassCount() : 0;
                    return BigDecimal.valueOf(totalCount - qualityPassCount);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return PmInspectionDataOverviewResp.builder()
                .orderCount(orderCount)
                .orderQuantity(orderQuantity)
                .nearOrderCount(nearOrderCount)
                .nearOrderQuantity(nearOrderQuantity)
                .errorOrderCount(errorOrderCount)
                .errorOrderQuantity(errorOrderQuantity)
                .build();
    }

    public List<PmInspectionCompleteOutputResp> getOutput(TimePeriodEnum periodEnum, InspectionTypeEnum inspectionTypeEnum) {

        List<PmInspectionCompleteOutputResp> resultList = new ArrayList<>();
        LocalDate today = LocalDate.now();

        switch (periodEnum) {
            case YEAR:
                int currentYear = today.getYear();
                for (int month = 1; month <= 12; month++) {
                    LocalDate startDate = LocalDate.of(currentYear, month, 1);
                    LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
                    String dateLabel = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                    resultList.add(calculateOutputForPeriod(startDate, endDate, dateLabel, periodEnum.getName(), inspectionTypeEnum));
                }
                break;
            case MONTH:
                LocalDate monthStart = today.withDayOfMonth(1);
                LocalDate monthEnd = today.withDayOfMonth(today.lengthOfMonth());
                
                for (int week = 1; week <= 4; week++) {
                    LocalDate weekStart = monthStart.plusWeeks(week - 1);
                    LocalDate weekEnd = weekStart.plusDays(6);
                    
                    if (weekStart.isBefore(monthStart)) {
                        weekStart = monthStart;
                    }
                    if (weekEnd.isAfter(monthEnd)) {
                        weekEnd = monthEnd;
                    }
                    
                    String dateLabel = "第" + week + "周";
                    resultList.add(calculateOutputForPeriod(weekStart, weekEnd, dateLabel, periodEnum.getName(), inspectionTypeEnum));
                }
                break;
            case WEEK:
                LocalDate weekStart = today.with(WeekFields.of(Locale.getDefault()).dayOfWeek(), 1);
                for (int day = 0; day < 7; day++) {
                    LocalDate date = weekStart.plusDays(day);
                    String dateStr = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
                    resultList.add(calculateOutputForPeriod(date, date, dateStr, periodEnum.getName(), inspectionTypeEnum));
                }
                break;
            default:
                throw new IllegalArgumentException("不支持此时间维度: " + periodEnum);
        }

        return resultList;
    }

    private PmInspectionCompleteOutputResp calculateOutputForPeriod(LocalDate startDate, LocalDate endDate, String dateStr, String periodName, InspectionTypeEnum inspectionTypeEnum) {
        BigDecimal totalOutput = BigDecimal.ZERO;
        BigDecimal totalOrderCount = BigDecimal.ZERO;

        if (inspectionTypeEnum == InspectionTypeEnum.QUALITY) {
            List<HyQmHoseAssemblyOrder> orders = hyQmHoseAssemblyOrderMapper.selectList(
                    new QueryWrapper<HyQmHoseAssemblyOrder>().lambda()
                            .isNotNull(HyQmHoseAssemblyOrder::getQualityTime)
                            .between(HyQmHoseAssemblyOrder::getQualityTime, startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay())
                            .eq(HyQmHoseAssemblyOrder::getQualityState, HoseInspectionStateEnum.PASSED.getValue())
            );

            totalOrderCount = BigDecimal.valueOf(orders.size());
            totalOutput = orders.stream()
                    .map(order -> order.getQualityPassCount() != null ? BigDecimal.valueOf(order.getQualityPassCount()) : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } else if (inspectionTypeEnum == InspectionTypeEnum.MILITARY) {
            List<HyQmProductCheckSubmit> orders = hyQmProductCheckSubmitMapper.selectList(
                    new QueryWrapper<HyQmProductCheckSubmit>().lambda()
                            .isNotNull(HyQmProductCheckSubmit::getQualityTime)
                            .between(HyQmProductCheckSubmit::getQualityTime, startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay())
                            .in(HyQmProductCheckSubmit::getQualityState, ProductCheckSubmitStateEnum.PENDING_PACKAGED.getValue(), ProductCheckSubmitStateEnum.PACKAGED.getValue())
            );

            totalOrderCount = BigDecimal.valueOf(orders.size());
            totalOutput = orders.stream()
                    .map(order -> order.getQualifiedQuantity() != null ? BigDecimal.valueOf(order.getQualifiedQuantity()) : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        return PmInspectionCompleteOutputResp.builder()
                .output(totalOutput)
                .orderNum(totalOrderCount)
                .dateStr(dateStr)
                .period(periodName)
                .build();
    }
}

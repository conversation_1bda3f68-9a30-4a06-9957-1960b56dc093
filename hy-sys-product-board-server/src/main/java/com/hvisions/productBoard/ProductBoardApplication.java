package com.hvisions.productBoard;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@EnableScheduling
@SpringBootApplication
@MapperScan(value = {"com.hvisions.**.mapper"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {
        "com.hvisions.framework.client",
        "com.hvisions.hiperbase.client",
})
public class ProductBoardApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(ProductBoardApplication.class, args);
    }

    /**
     * 可以使得项目用war包部署
     *
     * @param builder builder
     * @return builder
     */
    @Override

    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(ProductBoardApplication.class);
    }
}

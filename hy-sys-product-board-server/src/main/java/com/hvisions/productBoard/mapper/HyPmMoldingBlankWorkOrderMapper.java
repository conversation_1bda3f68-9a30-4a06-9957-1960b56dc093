package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.productBoard.entity.HyPmMoldingBlankWorkOrder;
import com.hvisions.productBoard.enums.MoldingTestTaskStateEnum;
import com.hvisions.productBoard.enums.MoldingWorkOrderStateEnum;
import com.hvisions.productBoard.req.CommonPageReq;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface HyPmMoldingBlankWorkOrderMapper extends BaseMapper<HyPmMoldingBlankWorkOrder> {

    /**
     * 查询关键订单列表
     */
    default List<HyPmMoldingBlankWorkOrder> selectListForKeyOrder(CommonPageReq req) {
        QueryWrapper<HyPmMoldingBlankWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(HyPmMoldingBlankWorkOrder::getTrialState, MoldingTestTaskStateEnum.PRODUCED.getValue())
                .ne(HyPmMoldingBlankWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCED_FINISH.getValue())
                .ne(HyPmMoldingBlankWorkOrder::getState, MoldingWorkOrderStateEnum.SUBMIT.getValue())
                .orderByAsc(HyPmMoldingBlankWorkOrder::getPlanDateTime);
        return this.selectList(queryWrapper);
    }

    /**
     * 查询试制订单列表
     */
    default List<HyPmMoldingBlankWorkOrder> selectListForTestOrder(CommonPageReq req) {
        QueryWrapper<HyPmMoldingBlankWorkOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(HyPmMoldingBlankWorkOrder::getTrialState,
                        MoldingTestTaskStateEnum.READY.getValue(),
                        MoldingTestTaskStateEnum.PRODUCING.getValue())
                .ne(HyPmMoldingBlankWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCED_FINISH.getValue())
                .ne(HyPmMoldingBlankWorkOrder::getState, MoldingWorkOrderStateEnum.SUBMIT.getValue())
                .orderByAsc(HyPmMoldingBlankWorkOrder::getPlanDateTime);
        return this.selectList(queryWrapper);
    }
}
package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.productBoard.entity.PmAssemblyWorkSubOrderOperationOutput;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface PmAssemblyWorkSubOrderOperationOutputMapper extends BaseMapper<PmAssemblyWorkSubOrderOperationOutput> {

    default List<PmAssemblyWorkSubOrderOperationOutput> selectListByCreateTimeBetween(LocalDateTime start, LocalDateTime end) {
        Wrapper<PmAssemblyWorkSubOrderOperationOutput> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkSubOrderOperationOutput.class)
                .ge(PmAssemblyWorkSubOrderOperationOutput::getCreateTime, start)
                .le(PmAssemblyWorkSubOrderOperationOutput::getCreateTime, end);

        return selectList(wrapper);
    }


}

package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.productBoard.entity.PmAssemblyWorkOrder;
import com.hvisions.productBoard.req.BiAssemblyKeyOrderReq;
import com.hvisions.productBoard.req.PmAssemblyKeyOrderReq;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface PmAssemblyWorkOrderMapper extends BaseMapper<PmAssemblyWorkOrder> {

    List<PmAssemblyWorkOrder> selectListByBiAssemblyKeyOrderReq(@Param("req") BiAssemblyKeyOrderReq req);

    List<PmAssemblyWorkOrder> selectListByStateIngAndLimitSize(@Param("limitSize") int limitSize);

    default List<PmAssemblyWorkOrder> selectListByPmAssemblyKeyOrderReq(PmAssemblyKeyOrderReq req) {
        Wrapper<PmAssemblyWorkOrder> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                .eq(PmAssemblyWorkOrder::getIsUrgency, true)
                .nested(StringUtils.isNoneBlank(req.getKeyword()), i -> i
                        .like(PmAssemblyWorkOrder::getCode, req.getKeyword())
                        .or()
                        .like(PmAssemblyWorkOrder::getSaleOrderCode, req.getKeyword())
                        .or()
                        .like(PmAssemblyWorkOrder::getProductModel, req.getKeyword())
                );

        return selectList(wrapper);
    }

    default List<PmAssemblyWorkOrder> selectListByPlanEndTimeBetween(LocalDateTime start, LocalDateTime end) {
        Wrapper<PmAssemblyWorkOrder> wrapper = Wrappers.lambdaQuery(PmAssemblyWorkOrder.class)
                .ge(PmAssemblyWorkOrder::getPlanEndTime, start)
                .le(PmAssemblyWorkOrder::getPlanEndTime, end);

        return selectList(wrapper);
    }


}

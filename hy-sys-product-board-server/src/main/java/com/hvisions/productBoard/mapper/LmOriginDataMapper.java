package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.productBoard.entity.LmOriginData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface LmOriginDataMapper extends BaseMapper<LmOriginData> {

    default List<LmOriginData> selectListOfStatusIng() {
        Wrapper<LmOriginData> wrapper = Wrappers.lambdaQuery(LmOriginData.class)
                .eq(LmOriginData::getStatus, 2);

        return selectList(wrapper);
    }

}

package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.productBoard.entity.LmContract;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

@Mapper
public interface LmContractMapper extends BaseMapper<LmContract> {

    default List<LmContract> selectListByIdIn(Collection<Long> ids) {
        Wrapper<LmContract> wrapper = Wrappers.lambdaQuery(LmContract.class)
                .in(LmContract::getId, ids);

        return selectList(wrapper);
    }

}

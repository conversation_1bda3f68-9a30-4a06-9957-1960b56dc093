package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.productBoard.entity.PmAssemblyWorkSubOrder;
import com.hvisions.productBoard.req.PmAssemblyWorkSubOrderPageQueryReq;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface PmAssemblyWorkSubOrderMapper extends BaseMapper<PmAssemblyWorkSubOrder> {

    // 没有工位参数
    List<PmAssemblyWorkSubOrder> selectList1(PmAssemblyWorkSubOrderPageQueryReq req);

    List<PmAssemblyWorkSubOrder> selectList2(PmAssemblyWorkSubOrderPageQueryReq req);

}

package com.hvisions.productBoard.mapper;

import com.hvisions.productBoard.dto.ContractDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface LmMapper {

    Long selectCountOfWorkingContract();

    BigDecimal selectQuantityOfWorkingContract();

    Long selectCountOfFinishedContractBetween(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    BigDecimal selectQuantityOfFinishedContractBetween(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    List<ContractDTO> selectListOfFinishedContractBetween2(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    List<ContractDTO> selectListOfFinishedContractBetween3(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

}

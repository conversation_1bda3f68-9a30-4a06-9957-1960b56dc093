package com.hvisions.productBoard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hvisions.productBoard.entity.QmHoseAssemblyOrder;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface QmHoseAssemblyOrderMapper extends BaseMapper<QmHoseAssemblyOrder> {

    default List<QmHoseAssemblyOrder> selectListByTrialAccomplishTimeBetween(LocalDateTime start, LocalDateTime end) {
        Wrapper<QmHoseAssemblyOrder> wrapper = Wrappers.lambdaQuery(QmHoseAssemblyOrder.class)
                .ge(QmHoseAssemblyOrder::getTrialAccomplishTime, start)
                .le(QmHoseAssemblyOrder::getTrialAccomplishTime, end);

        return selectList(wrapper);
    }

}

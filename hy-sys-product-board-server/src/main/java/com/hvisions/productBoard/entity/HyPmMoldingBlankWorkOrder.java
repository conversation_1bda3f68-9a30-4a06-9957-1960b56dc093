package com.hvisions.productBoard.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 原始记录单
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("sys_pms.hy_pm_molding_blank_work_order")
public class HyPmMoldingBlankWorkOrder extends SysBase {
    /**
    * 实际生产日期
    */
    private Date actualEndDateTime;

    private BigDecimal actualQuantity;

    /**
    * 实际生产日期
    */
    private Date actualStartDateTime;

    /**
    * 毛坯生产计划
    */
    private Integer blankWorkPlanId;

    /**
    * 原始记录单号
    */
    private String code;

    /**
    * 内管颜色
    */
    private String color;

    /**
    * 执行参数编号
    */
    private String executeParam;

    /**
    * 是否可编辑
    */
    private Integer isDisabled;

    /**
    * 是否损耗
    */
    private String isLoss;

    /**
    * 操作人账号
    */
    private String operatorAccount;

    /**
    * 操作人
    */
    private String operatorId;

    /**
    * 操作人名称
    */
    private String operatorName;

    /**
    * 计划生产日期
    */
    private Date planDateTime;

    private BigDecimal planQuantity;

    /**
    * 产品批次
    */
    private String productBatchNumber;

    /**
    * 产品编码
    */
    private String productCode;

    /**
    * 产品规格
    */
    private String productEigenvalue;

    /**
    * 产品名称
    */
    private String productName;

    /**
    * 推压记录编号
    */
    private String pushRecordCode;

    /**
    * 备注
    */
    private String remark;

    /**
    * 工艺规程
    */
    private String routeRule;

    /**
    * 序号
    */
    private Integer seq;

    /**
    * 班次编码
    */
    private String shiftCode;

    /**
    * 班次
    */
    private Integer shiftId;

    /**
    * 班次名称
    */
    private String shiftName;

    /**
    * 生产状态
    */
    private Integer state;

    /**
    * 生产状态
    */
    private String stateName;

    /**
    * 班组编码
    */
    private String teamCode;

    /**
    * 班组id
    */
    private Integer teamId;

    /**
    * 班组名称
    */
    private String teamName;

    /**
    * 试制状态
    */
    private Integer trialState;

    /**
    * 试制状态
    */
    private String trialStateName;

    /**
    * 任务类型
    */
    private Integer type;

    /**
    * 任务类型
    */
    private String typeName;

    /**
    * 单位
    */
    private String unitSymbol;

    /**
    * 关联标识：同一毛坯生产计划
    */
    private String uuid;

    /**
    * 关联标识：压坯、推压、自检
    */
    private String uuid2;

    /**
    * 作业指导书
    */
    private String workGuideBook;

    /**
    * 生产任务单号
    */
    private String workOrderCode;

    /**
    * 生产任务单号id
    */
    private Integer workOrderId;

    /**
    * 排序
    */
    private Integer sortSeq;

    private BigDecimal planBlackMaterialBottleQuantity;

    private BigDecimal planMainMaterialBottleQuantity;

    private BigDecimal planMinorMaterialBottleQuantity;

    /**
    * 送检单号
    */
    private String inspectionOrderCode;

    /**
    * 是否已送检
    */
    private Integer isInspectionOrdered;

    /**
    * 配料原始记录单号
    */
    private String peiLiaoOrderCode;

    /**
    * 图号
    */
    private String drawingNum;

    /**
    * 测量器具
    */
    private String measureAppliance;

    /**
    * 卡尺编号
    */
    private String caliper;

    /**
    * 卷尺编号
    */
    private String tapeline;

    /**
    * 过程状态
    */
    private Integer processState;

    /**
    * 过程状态
    */
    private String processStateName;

    /**
    * 图纸版号
    */
    private String drawingVersion;

    /**
    * 图纸id
    */
    private Integer materialRouteId;

    /**
    * 批次管号
    */
    private String batchPipeNo;

    /**
    * 批次图号
    */
    private String batchDrawingNum;

    /**
    * 送检状态
    */
    private Integer inspectionState;

    /**
    * 退回前工序
    */
    private Integer cancelProcess;

    /**
    * 是否转试制
    */
    private Integer isTrialProduce;
}
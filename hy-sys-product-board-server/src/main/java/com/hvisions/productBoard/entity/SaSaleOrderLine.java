package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 备货需求单明细
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_sale.hy_sa_sale_order_line")
public class SaSaleOrderLine extends SysBase {

    /**
     * 创建日期
     */
    private LocalDate createDate;

    /**
     * 制单人
     */
    private String creatorName;

    /**
     * 备货需求号
     */
    private String saleOrderCode;

    /**
     * 对方合同号
     */
    private String contractCode;

    /**
     * 军种
     */
    private String armyType;

    /**
     * 机型
     */
    private String machineType;

    /**
     * 客户
     */
    private Integer customerId;

    /**
     * 客户简称
     */
    private String customerName;

    /**
     * 客户全称
     */
    private String customerFullName;

    /**
     * 购货单位
     */
    private String customerCompany;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 产品规格
     */
    private String productEigenvalue;

    /**
     * 图号
     */
    private String drawingNo;

    /**
     * 单位id
     */
    private Integer productUom;

    /**
     * 单位名称
     */
    private String productUomName;

    /**
     * 单位符号
     */
    private String productUomDesc;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 含税单价
     */
    private BigDecimal unitPriceTax;

    /**
     * 价税合计
     */
    private BigDecimal totalPrice;

    /**
     * 交货日期
     */
    private LocalDate deliverDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 计划数量
     */
    private BigDecimal planCount = BigDecimal.ZERO;

    /**
     * 下发数量
     */
    private BigDecimal dispatchCount = BigDecimal.ZERO;

    /**
     * 生产完成数量
     */
    private BigDecimal productionCount = BigDecimal.ZERO;

    /**
     * 生产取消数量
     */
    private BigDecimal productionCancelCount = BigDecimal.ZERO;

    /**
     * 质检完成数量
     */
    private BigDecimal qualityCount = BigDecimal.ZERO;

    /**
     * 军检完成数量
     */
    private BigDecimal militaryQualityCount = BigDecimal.ZERO;

    /**
     * 入库完成数量
     */
    private BigDecimal wareCount = BigDecimal.ZERO;

    /**
     * 发货完成数量
     */
    private BigDecimal deliverCount = BigDecimal.ZERO;

    /**
     * 开票完成数量
     */
    private BigDecimal invoiceCount = BigDecimal.ZERO;

    /**
     * 关闭状态
     */
    private Integer closeState;

    /**
     * 下派状态
     */
    private Integer dispatchState;

    /**
     * 发货状态
     */
    private Integer shippingState;

    /**
     * 预计发货数量
     */
    private BigDecimal predictShippingCount = BigDecimal.ZERO;

    /**
     * 可发货数量
     */
    private BigDecimal shipmentCount = BigDecimal.ZERO;

    /**
     * 部门
     */
    private String department;

    /**
     * 物料分组
     */
    private String materialGroupCode;
    /**
     * 物料分组
     */
    private String materialGroupName;

    /**
     * 物料分组类型
     */
    private String materialGroupTypeCode;
    /**
     * 物料分组类型
     */
    private String materialGroupTypeName;

    /**
     * 质检策略：-1: 车间检验，-2: 自检，-3: 免检，其他数值: 质检标准
     */
    private Integer qualityStrategy;
    /**
     * 质检策略/质检标准名称
     */
    private String qualityStrategyName;

    /**
     * 半成品物料编码
     */
    private String semiMaterialCode;
    /**
     * 半成品质检标准ID
     */
    private Integer semiQualityStandardId;
    /**
     * 半成品质检标准编码
     */
    private String semiQualityStandardCode;
    /**
     * 半成品质检标准名称
     */
    private String semiQualityStandardName;
    /**
     * 半成品质检标准版本
     */
    private String semiQualityStandardVersion;

    /**
     * 待检量
     */
    private BigDecimal waitQualityCount = BigDecimal.ZERO;
    /**
     * 待入库量
     */
    private BigDecimal waitWareCount = BigDecimal.ZERO;
}

package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * BOM
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_hiper_base.hv_bm_bom")
public class BmBom extends SysBase {

    /**
     * bom编码（非空）
     */
    private String bomCode;
    /**
     * bom名称（非空）
     */
    private String bomName;
    /**
     * bom描述
     */
    private String bomDesc;
    /**
     * bom版本（非空）
     */
    private String bomVersions;
    /**
     * bom状态
     */
    private Integer bomStatus;
    /**
     * 数量
     */
    private BigDecimal bomCount;
    /**
     * 计量单位
     */
    private Integer unitId;

    /**
     * SAP BOM编码
     */
    private String sapBomCode;

    /**
     * 部门
     */
    private String department;

}

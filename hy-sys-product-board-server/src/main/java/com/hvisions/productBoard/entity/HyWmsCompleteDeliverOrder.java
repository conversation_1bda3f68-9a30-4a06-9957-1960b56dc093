package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 完工入库单
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_wms.hy_wms_complete_deliver_order")
public class HyWmsCompleteDeliverOrder extends SysBase {
    private BigDecimal actualQuantity;

    /**
     * 军种
     */
    private String armType;

    /**
     * 完工入库单号
     */
    private String code;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 异常原因
     */
    private String exceptionMsg;

    /**
     * 入库日期
     */
    private Date finishDate;

    /**
     * 来源库位
     */
    private String fromLocation;

    /**
     * 来源库位
     */
    private String fromLocationCode;

    /**
     * 备注
     */
    private String memo;

    /**
     * 包装任务单号
     */
    private String pkgOrderCode;

    private BigDecimal planCount;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 销售单号
     */
    private String saleOrderCode;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 提交次数
     */
    private Integer submitTimes;

    /**
     * 目标库位
     */
    private String targetLocation;

    /**
     * 目标库位
     */
    private String targetLocationCode;

    /**
     * 操作人
     */
    private String userName;

    /**
     * SAP生产订单号
     */
    private String sapOrderCode;

    /**
     * 提交状态
     */
    private Integer submitState;

    /**
     * 部门
     */
    private String department;

    /**
     * 物料转换任务单号
     */
    private String materialConverCode;

    /**
     * 业务单号类型
     */
    private Integer bizOrderType;

    /**
     * 业务单号类型
     */
    private String bizOrderTypeName;
}
package com.hvisions.productBoard.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 软管组件检验单
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("sys_qms.hy_qm_hose_assembly_order")
public class HyQmHoseAssemblyOrder extends SysBase {
    /**
    * 军种
    */
    private String armType;

    /**
    * 批次
    */
    private String batchNum;

    /**
    * 卡尺
    */
    private String callipers;

    /**
    * 是否打印合格证
    */
    private Integer certificate;

    /**
    * 客户代表日期
    */
    private Date customerTime;

    /**
    * 客户代表
    */
    private Integer customerUserId;

    /**
    * 客户代表
    */
    private String customerUserName;

    /**
    * 检验状态
    */
    private Integer departmentState;

    /**
    * 图号
    */
    private String drawNum;

    /**
    * 图纸ID
    */
    private Integer drawingFileId;

    /**
    * 图纸状态
    */
    private Integer drawingState;

    /**
    * 最新图纸更新时间
    */
    private Date drawingUpdateDateTime;

    /**
    * 图纸版本
    */
    private String drawingVersion;

    /**
    * 最新图纸版本
    */
    private String drawingVersionLatest;

    /**
    * 干燥时间
    */
    private Date dryingTime;

    /**
    * 是否提前检验
    */
    private Integer earlierCheckout;

    /**
    * 总装人员
    */
    private Integer finalUserId;

    /**
    * 总装人员2
    */
    private Integer finalUserId2;

    /**
    * 总装人员
    */
    private String finalUserName;

    /**
    * 总装人员2
    */
    private String finalUserName2;

    /**
    * 客户代表日期
    */
    private Date finalUserTime;

    /**
    * 总装人员签字日期2
    */
    private Date finalUserTime2;

    /**
    * 止通规
    */
    private String gauge;

    /**
    * 是否提前检验(隐藏)
    */
    private Integer hideEarlierCheckout;

    /**
    * 称
    */
    private String hoseCall;

    private Boolean isActivation;

    private Boolean isCreateReviewOrder;

    private Boolean isQuSubmit;

    private Boolean isReturn;

    private Boolean isSave;

    private Boolean isSplit;

    /**
    * 机型
    */
    private String machineType;

    /**
    * 物料代码
    */
    private String materialCode;

    /**
    * 物料ID
    */
    private Integer materialId;

    /**
    * 物料名称
    */
    private String materialName;

    /**
    * 产品数据库编码
    */
    private String materialRouteCode;

    /**
    * 产品数据库id
    */
    private Integer materialRouteId;

    /**
    * 软管检验单号
    */
    private String orderCode;

    /**
    * 其他试验
    */
    private String otherTests;

    /**
    * 生产日期
    */
    private Date produceTime;

    /**
    * 产品规格
    */
    private String productEigenvalue;

    /**
    * 产品型号
    */
    private String productModel;

    /**
    * 质检合格数
    */
    private Integer qualityPassCount;

    /**
    * 检验状态
    */
    private Integer qualityState;

    /**
    * 质检日期
    */
    private Date qualityTime;

    /**
    * 质检类型
    */
    private Integer qualityType;

    /**
    * 质检人员
    */
    private Integer qualityUserId;

    /**
    * 质检人员
    */
    private String qualityUserName;

    /**
    * 备注
    */
    private String remark;

    /**
    * 直尺
    */
    private String ruler;

    /**
    * 销售订单号
    */
    private String saleOrderCode;

    /**
    * 销售订单明细ID
    */
    private Integer saleOrderLineId;

    /**
    * 对方合同号
    */
    private String sideContract;

    /**
    * 存放位置
    */
    private String storageLocation;

    /**
    * 验收提交单号
    */
    private String submitOrderCode;

    /**
    * 订货单位
    */
    private String supplierName;

    /**
    * 生产数量
    */
    private Integer totalCount;

    /**
    * 试验完成日期
    */
    private Date trialAccomplishTime;

    /**
    * 试验完成日期2
    */
    private Date trialAccomplishTime2;

    /**
    * 试验合格数
    */
    private Integer trialPassCount;

    /**
    * 试验日期
    */
    private Date trialTime;

    /**
    * 试验日期2
    */
    private Date trialTime2;

    /**
    * 试验人员
    */
    private Integer trialUserId;

    /**
    * 试验人员2
    */
    private Integer trialUserId2;

    /**
    * 试验人员
    */
    private String trialUserName;

    /**
    * 试验人员2
    */
    private String trialUserName2;

    /**
    * 是否创建完成不合格流程单子
    */
    private Integer unqualified;

    /**
    * 生产任务单号
    */
    private String workOrderCode;

    /**
    * 原始记录单
    */
    private String workSubOrderCode;

    /**
    * 提前质检日期
    */
    private Date earlyQualityTime;

    /**
    * 提前质检人员
    */
    private Integer earlyQualityUserId;

    /**
    * 提前质检人员
    */
    private String earlyQualityUserName;

    /**
    * 科研任务单号
    */
    private String researchOrderCode;

    /**
    * 科研任务单id
    */
    private Integer researchOrderLineId;

    /**
    * 单据类型
    */
    private Integer orderType;

    /**
    * 用于存储其他信息的备注
    */
    private String remark2;

    /**
    * 选择硫化类型的次数
    */
    private Integer orderTypeTimes;

    /**
    * 是否特殊软管
    */
    private Integer isSpecial;

    /**
    * 关闭前检验状态
    */
    private Integer qualityStateBefore;

    /**
    * 试验不合格数
    */
    private Integer trialUnPassCount;

    /**
    * 放行数量
    */
    private Integer releaseQty;

    /**
    * 父原始记录单号
    */
    private String parentWorkSubOrderCode;

    /**
    * 是否紧急
    */
    private Integer isUrgency;

    /**
    * 是否试验
    */
    private Integer isTest;

    /**
    * 检验次数
    */
    private Integer testTimes;
}
package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.math.BigDecimal;


@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_wms.hy_wms_deliver_order")
public class WmDeliverOrder extends SysBase {

    /**
     * 移库单号
     */
    @Column(columnDefinition = "varchar(255) comment '移库单号'")
    private String code;
    /**
     * 业务类型 DeliverOperationEnum
     */
    @Column(columnDefinition = "int comment '业务类型'")
    private Integer operation;
    /**
     * 单据类别 （生产领料，科研领料） DeliverOrderTypeEnum
     */
    @Column(columnDefinition = "int comment '领料类型'")
    private Integer orderType;
    // BackTypeEnum 正常退料 不合格退料
    @Column(columnDefinition = "int comment '退料类型'")
    private Integer backType;
    /**
     * 车间退料 类别 【金属件 收缩套 软管】
     */
    @Column(columnDefinition = "varchar(255) comment '类别'")
    private String category;

    @Column(columnDefinition = "int comment '业务单号类型'")
    private Integer bizOrderType;
    @Column(columnDefinition = "varchar(100) comment '业务单号类型'")
    private String bizOrderTypeName;
    /**
     * 业务单号
     */
    @Column(columnDefinition = "varchar(255) comment '业务单号'")
    private String orderCode;
    /**
     * 状态 DeliverOrderStateEnum 发出方状态
     */
    @Column(columnDefinition = "int comment '发出方状态'")
    private Integer state;


    /**
     * 状态 DeliverOrderSubmitStateEnum 提交SAP状态
     */
    @Column(columnDefinition = "int comment '提交SAP状态'")
    private Integer submitState;
    /**
     * 需求量
     */
    @Column(columnDefinition = "decimal(19,3) comment '需求量'")
    private BigDecimal needQuantity = BigDecimal.ZERO;
    /**
     * 领料数量
     */
    @Column(columnDefinition = "decimal(19,3) comment '计划领料数量'")
    private BigDecimal planQuantity = BigDecimal.ZERO;
    /**
     * 发出数量
     */
    @Column(columnDefinition = "decimal(19,3) comment '发出数量'")
    private BigDecimal fromQuantity = BigDecimal.ZERO;

    /**
     * 接受数量
     */
    @Column(columnDefinition = "decimal(19,3) comment '接受数量'")
    private BigDecimal targetQuantity = BigDecimal.ZERO;
    /**
     * 已提交数量
     */
    @Column(columnDefinition = "decimal(19,3) comment '已提交数量'")
    private BigDecimal submitQuantity = BigDecimal.ZERO;


    @Column(columnDefinition = "decimal(19,3) comment '补领数量'")
    private BigDecimal supplementQuantity = BigDecimal.ZERO;
    /**
     * 来源库位
     */
    @Column(columnDefinition = "varchar(255) comment '来源库位'")
    private String fromLocationCode;
    @Column(columnDefinition = "varchar(255) comment '来源库位'")
    private String fromLocation;

    /**
     * 目标库位
     */
    @Column(columnDefinition = "varchar(255) comment '目标库位'")
    private String targetLocationCode;
    @Column(columnDefinition = "varchar(255) comment '目标库位'")
    private String targetLocation;

    /**
     * 领料部门
     */
    @Column(columnDefinition = "varchar(255) comment '领料部门'")
    private String department;

    /**
     * 提交次数
     */
    @Column(columnDefinition = "int comment '提交次数'")
    private Integer submitTimes = 1;
    /**
     * 物料组
     */
    @Column(columnDefinition = "varchar(255) comment '物料组'")
    private String materialGroup;
    @Column(columnDefinition = "varchar(255) comment '物料组'")
    private String materialGroupName;

    /**
     * 创建人
     */
    @Column(columnDefinition = "varchar(255) comment '创建人'")
    private String creatorName;

    @Column(columnDefinition = "varchar(255) comment '销售订单号'")
    private String saleOrderCode;

    @Column(columnDefinition = "varchar(255) comment '产品型号'")
    private String productModel;

    @Column(columnDefinition = "varchar(255) comment '产品规格'")
    private String productEigenvalue;

    @Column
    private Boolean isClose = false;

    /**
     * 打印相关
     */
    @Column(columnDefinition = "int default 0 comment '打印状态'  ")
    private Integer printState;

    @Column(columnDefinition = "int default 0 comment '打印次数'")
    private Integer printTimes;

    @Column(columnDefinition = "varchar(255) comment '备注'")
    private String remark;

    @Column(columnDefinition = "varchar(255) comment '预留单号'")
    private String rsnum;

    @Column(columnDefinition = "decimal(19,3) comment '科研消耗数量'")
    private BigDecimal consumeQuantity = BigDecimal.ZERO;

}

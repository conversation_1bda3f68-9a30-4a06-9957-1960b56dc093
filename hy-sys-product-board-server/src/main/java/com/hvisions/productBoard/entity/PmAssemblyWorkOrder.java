package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 五部总装任务单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_pms.hy_pm_assembly_work_order")
public class PmAssemblyWorkOrder extends SysBase {

    /**
     * 任务单编码
     */
    private String code;

    /**
     * 是否紧急
     */
    private Boolean isUrgency;

    /**
     * 是否试验任务单
     */
    private Boolean isTest;

    /**
     * 是否销售订单
     */
    private Boolean isSaleOrder;

    /**
     * 是否科研任务单
     */
    private Boolean isResearchOrder;

    /**
     * 总装生产计划明细ID
     */
    private Integer workPlanLineId;

    /**
     * 销售订单->生产计划->生产任务单  生产
     * 科研任务单->生产计划->生产任务单 科研
     * 增强批次余额 -> 试验任务单(正常流程)  试验
     * 增强批次余额 -> 试验任务单(非正常流程) 试验
     */

    /**
     * 工单类型
     */
    private Integer type;
    /**
     * 工单类型
     */
    private String typeName;

    /**
     * 用途
     */
    private Integer useWay;
    /**
     * 用途
     */
    private String useWayName;

    /**
     * 生成模式
     */
    private String productionMode;

    /**
     * 生产类型
     */
    private Integer productionType;
    /**
     * 生产类型
     */
    private String productionTypeName;

    /**
     * 产品类型
     */
    private Integer productType;
    /**
     * 产品类型
     */
    private String productTypeName;

    /**
     * 军种
     */
    private String armyType;

    // 机型不被需要了
    /**
     * 机型
     */
    private String machineType;

    /**
     * 交付单位
     */
    private String customCompany;

    /**
     * 销售订单
     */
    private String saleOrderCode;

    /**
     * 销售订单明细ID
     */
    private Integer saleOrderLineId;

    /**
     * 销售订单合同号
     */
    private String saleOrderContractCode;

    /**
     * 科研任务单号
     */
    private String researchOrderCode;

    /**
     * 科研任务订单明细ID
     */
    private Integer researchOrderLineId;

    /**
     * 交付单位
     */
    private Integer customerId;
    /**
     * 交付单位
     */
    private String customerName;

    /**
     * 物料ID
     */
    private Integer materialId;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 规格
     */
    private String eigenvalue;
    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 物料单位
     */
    private String materialUnitName;
    /**
     * 物料单位
     */
    private String materialUnitSymbol;

    /**
     * 图号
     */
    private String drawingNo;

    /**
     * 批次
     */
    private String materialBatchNum;

    /**
     * 实际批次
     */
    private String actualMaterialBatchNum;

    /**
     * BOM ID
     */
    private Integer bomId;

    /**
     * BOM 编码
     */
    private String bomCode;

    /**
     * BOM 名称
     */
    private String bomName;

    /**
     * BOM 版本
     */
    private String bomVersion;

    /**
     * 工艺路线ID
     */
    private Integer materialRouteId;

    /**
     * 工艺路线编码
     */
    private String materialRouteCode;

    /**
     * 工艺路线名称
     */
    private String materialRouteName;

    /**
     * 工艺路线备注
     */
    private String materialRouteRemark;

    /**
     * 图纸版本
     */
    private String drawingVersion;

    /**
     * 最新图纸版本
     */
    private String drawingVersionLatest;

    /**
     * 图纸状态
     */
    private Integer drawingState;

    /**
     * 是否图纸需要更新
     */
    private Boolean isDrawingNeedUpdate;

    /**
     * 图纸更新时间
     */
    private LocalDateTime drawingUpdateDateTime;

    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime planEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime actualEndTime;

    /**
     * 计划数量
     */
    private BigDecimal planQuantity;

    /**
     * 下发原始记录单数量汇总
     */
    private BigDecimal subQuantity;

    /**
     * 实际产出数量
     */
    private BigDecimal productQuantity;

    /**
     * 合格数量
     */
    private BigDecimal qualifiedQuantity;

    /**
     * 不合格数量
     */
    private BigDecimal unqualifiedQuantity;

    /**
     * 生产状态
     */
    private Integer state;

    /**
     * 领料状态
     */
    private Integer deliverState;

    /**
     * 领料单号
     */
    private String deliverOrderCode;

    /**
     * 交货日期
     */
    private LocalDateTime deliveryDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图纸软管长度
     */
    private String drawingHoseLength;

    /**
     * 工作压力
     */
    private String workingPressure;

    /**
     * 耐压压力
     */
    private String withstandPressure;

    /**
     * 收缩套压力
     */
    private String shrink;

    /**
     * 是否需要二维码
     */
    private String qrcode;

    /**
     * 组件段数
     */
    private Integer multiNum;

    /**
     * 车间ID
     */
    private Integer areaId;

    /**
     * 产线Id
     */
    private Integer cellId;

    ////////////////////////////////////////////////// 原始记录单 //////////////////////////////////////////////////

    /**
     * 原始记录单下一个序号
     */
    private Integer subOrderNextSeq = 1;
}

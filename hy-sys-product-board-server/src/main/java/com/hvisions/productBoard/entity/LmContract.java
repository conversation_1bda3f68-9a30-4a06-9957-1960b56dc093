package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * LIMS 委托单
 */
@Data
@TableName(value = "sys_lims2.contract")
public class LmContract {

    private Long id;

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 合同开始日期
     */
    private String contractStartDate;

    /**
     * 合同结束日期
     */
    private String contractEndDate;

    /**
     * 合同天数
     */
    private double workDays;

    /**
     * 合同完成日期
     */
    private String finishDate;

    /**
     * 合同模板ID ContractSTemplateEnum
     * 2 材料检测委托单
     * 3 内管检测委托单
     * 4 软管组件委托单
     */
    @TableField(value = "contract_templetid")
    private int contractTempletID;

    /**
     * 合同自动编号状态
     */
    private int contractAutoNumStatus;

    /**
     * 合同自动编号前缀
     */
    private String contractAutoNumPrefix;

    /**
     * 合同自动编号
     */
    private int contractAutoNum;

    /**
     * 客户ID
     */
    @TableField(value = "customerid")
    private int customerID;

    // 这个逻辑
    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合同费用
     */
    private double contractFees;

    /**
     * 合同付款
     */
    private double contractPay;

    /**
     * 币种名称
     */
    private String currencyName;

    /**
     * 汇率
     */
    private double exchangeRate;

    /**
     * 费用说明
     */
    private String feeExp;

    /**
     * 创建部门
     */
    private String createDep;

    /**
     * 状态 ContractStatusEnum
     */
    private int status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 收费标准
     */
    private double chargeStandard;

    /**
     * 优惠金额
     */
    private double preferentialMoney;

    /**
     * 取样方式
     */
    private String takeTypeName;

    /**
     * 取样说明
     */
    private String takeNotes;

    /**
     * 旧合同编号
     */
    private String oldContractNumber;

    /**
     * 申请编号
     */
    private String applicationNumber;

    /**
     * 费用模板数据
     */
    private String feeXmlTempletData;

    /**
     * 合同格式
     */
    private String contractFormat;

    /**
     * 合同参数
     */
    private String contractPara;

    /**
     * 合同数据
     */
    private String contractData;

    /**
     * 合同费用类型
     */
    private int contractFeesType;

    /**
     * 最近操作
     */
    private String recentOperation;

    /**
     * 发票金额
     */
    private double invoiceAmount;

    /**
     * 收费金额
     */
    private double chargeAmount;

    /**
     * 发票收费金额
     */
    private double invoiceChargeAmount;

    /**
     * 子机构
     */
    private String subOrg;

    /**
     * 最近操作备注
     */
    private String recentOperationMemo;

    /**
     * 附件
     */
    private String attachs;

    // 联系人姓名
    /**
     * 联系人姓名F0
     */
    private String contactName;

    /**
     * 联系人电话F1
     */
    private String contactPhone;


    /**
     * 是否抽样F3 是|否
     */
    private String sampleFlg;

    /**
     * 送样日期F5
     */
    private String sendDate;


    /**
     * 报告抬头F6
     */
    private String reportTitle;

    /**
     * 判定报告F7 判定|不判定
     */
    private String reportFlg;


    /**
     * 备注F8
     */
    private String memo;


    /**
     * 是否军方代表F9 是|否
     */
    private String armyFlg;

    /**
     * F11 报告类别 报告类别|内部报告
     */
    private String reportType;


    /**
     * 抽样人F20
     */
    private String sampler;

    /**
     * 抽样时间F21
     */
    private String samplerTime;

    /**
     * 自检费用F23
     */
    private String selfCheckFees;

    /**
     * 委外费用F24
     */
    private String outFees;

    /**
     * 状态名备注F25
     */
    private String statusNameMemo;

    /**
     * （委外方）F27
     */
    private String outTypeName;

    /**
     * 是否课题F28 是|否
     */
    private String projectFlg;

    /**
     * 用来记录当前数据在的各个从流程名称
     */
    private String processNames;

    /**
     * 样品名称
     */
    private String sampleName;

    /**
     * 工作日
     */
    private Integer deadlineDays;

    /**
     * 是否数据录入回退的 1
     */
    private Integer back;

    /**
     * 各个环节流程名称
     */
    private String timeNods;


    //===================内管多出的字段 =====================

    /**
     * 用户单位
     */
    private String userOrg;

    /**
     * 委托单位
     */
    private String entrustOrg;

    /**
     * 委托联系电话
     */
    private String entrustPhone;

    /**
     * 委托地址
     */
    private String entrustAddress;

    /**
     * 委托邮编
     */
    private String entrustPostCode;

    /**
     * 制造单位
     */
    private String manufacturerOrg;

    /**
     * 试验性质 出厂  科研  鉴定
     */
    private String testNature;

}

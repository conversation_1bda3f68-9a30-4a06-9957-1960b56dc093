package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import java.util.Date;

/**
 * 软管组件检验单明细
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_qms.hy_qm_hose_assembly_order_line")
public class QmHoseAssemblyOrderLine extends SysBase {

    /**
     * 软管检验单号
     */
    @Column(columnDefinition = "varchar(255) comment '软管检验单号'")
    private String orderCode;
    /**
     * 生产任务单号
     */
    @Column(columnDefinition = "varchar(255) comment '生产任务单号'")
    private String workOrderCode;

    @Column(columnDefinition = "varchar(255) comment '销售单号'")
    @ApiModelProperty(value = "销售单号")
    private String saleOrderCode;

    @Column(columnDefinition = "int comment '销售明细id'")
    @ApiModelProperty(value = "销售明细id")
    private Integer saleOrderLineId;

    @Column(columnDefinition = "varchar(255) comment '验收提交单号'")
    @ApiModelProperty(value = "验收提交单号")
    private String submitOrderCode;
    /**
     * 原始记录单
     */
    @Column(columnDefinition = "varchar(255) comment '原始记录单'")
    private String workSubOrderCode;

    @Column(columnDefinition = "varchar(255) comment '管号前缀'")
    private String pipeNoPrefix;

    @Column(columnDefinition = "varchar(255) comment '管号后缀'")
    private String pipeNoSuffix;

    @Column(columnDefinition = "varchar(255) comment '质检类型'")
    @ApiModelProperty(value = "质检类型")
    private Integer qualityType;

    @Column(columnDefinition = "varchar(255) comment '产品型号'")
    private String productModel;
    /**
     * 产品编码
     */
    @Column(columnDefinition = "varchar(255) comment '产品编码'")
    private String productCode;
    @Column(columnDefinition = "int comment '物料ID'")
    private Integer materialId;
    /**
     * 产品名称
     */
    @Column(columnDefinition = "varchar(255) comment '产品名称'")
    private String productName;
    /**
     * 产品规格
     */
    @Column(columnDefinition = "varchar(255) comment '产品规格'")
    private String productEigenvalue;
    /**
     * 软管图纸长度
     */
    @Column(columnDefinition = "varchar(255) comment '软管图纸长度'")
    private String partBlueprintLength;

    /**
     * 批次
     */
    @Column(columnDefinition = "varchar(255) comment '批次'")
    private String batchNum;

    @Column(columnDefinition = "int comment '根数'")
    private Integer quantity;

    @Column(columnDefinition = "bit comment '是否特殊'")
    private Boolean isSpecial;

    /**
     * 批次管号
     */
    @Column(columnDefinition = "varchar(255) comment '批次管号'")
    private String pipeNo;

    @Column(columnDefinition = "int comment '批次管号id'")
    private Integer pipeId;

    /**
     * 工作压力（Mpa）
     */
    @Column(columnDefinition = "varchar(255) comment '工作压力（Mpa）'")
    private String workingPressure;

    /**
     * 工作压力2（Mpa）
     */
    @Column(columnDefinition = "varchar(255) comment '工作压力2（Mpa）'")
    private String workingPressure2;

    /**
     * 耐压实验（Mpa）
     */
    @Column(columnDefinition = "varchar(255) comment '耐压实验（Mpa）'")
    private String withstandVoltage;

    /**
     * 耐压实验2（Mpa）
     */
    @Column(columnDefinition = "varchar(255) comment '耐压实验2（Mpa）'")
    private String withstandVoltage2;

    /**
     * 其他实验
     */
    @Column(columnDefinition = "varchar(255) comment '其他实验'")
    private String otherExperiments;

    /**
     * 其他实验
     */
    @Column(columnDefinition = "varchar(255) comment '其他实验2'")
    private String otherExperiments2;


    /**
     * 软管长度
     */
    @Column(columnDefinition = "varchar(255) comment '软管长度'")
    private String partLength;
    /**
     * 扣压外径
     */
    @Column(columnDefinition = "varchar(100) comment '扣压外径'")
    private String withholdDiameterMin;
    /**
     * 扣压外径
     */
    @Column(columnDefinition = "varchar(100) comment '扣压外径'")
    private String withholdDiameterMax;

    /**
     * 1合格    10不合格
     */
    @Column(columnDefinition = "varchar(255) comment '外观'")
    private String appearance;
    /**
     * 过球
     */
    @Column(columnDefinition = "varchar(255) comment '过球'")
    private String ball;

    @Column(columnDefinition = "varchar(255) comment '重量（kg）'")
    private String weight;
    /**
     * 左端螺纹
     */
    @Column(columnDefinition = "varchar(255) comment '左端螺纹'")
    private String leftThread;
    /**
     * 右端螺纹
     */
    @Column(columnDefinition = "varchar(255) comment '右端螺纹'")
    private String rightThread;
    /**
     * 质检  StatusEnum
     */
    @Column(columnDefinition = "int comment '质检'")
    private Integer quality;

    /**
     * 军检/下场验收  StatusEnum
     */
    @Column(columnDefinition = "int comment '军检'")
    private Integer armQuality;

    @Column(columnDefinition = "varchar(255) comment '外观1'")
    private String appearanceOne;
    @Column(columnDefinition = "int comment '质检1'")
    private Integer qualityOne;

    @ApiModelProperty(value = "描述")
    @Column(columnDefinition = "varchar(255) comment '描述'")
    private String description ;

    @ApiModelProperty(value = "补充说明")
    @Column(columnDefinition = "varchar(255) comment '补充说明'")
    private String supplementaryNotes;

    @ApiModelProperty(value = "是否二次检验()")
    @Column(columnDefinition = "int comment '是否二次检验'")
    private Integer secondaryCheckout;

    @Column(columnDefinition = "varchar(100) comment '软管长度1'")
    private String partLengthMin;
    @Column(columnDefinition = "decimal(11,2) comment '软管长度1'")
    private String partLengthMax;

    @Column(columnDefinition = "decimal(11,2) comment '扣压外径1'")
    private String withholdDiameterToMin;

    @Column(columnDefinition = "decimal(11,2) comment '扣压外径1'")
    private String  withholdDiameterToMax;
    @Column(columnDefinition = "varchar(255) comment '过球1'")
    private String ballTo;
    @Column(columnDefinition = "varchar(255) comment '重量最小值'")
    private String weightMin;
    @Column(columnDefinition = "varchar(255) comment '重量最大值'")
    private String weightMax;
    @Column(columnDefinition = "varchar(255) comment '工作压力（Mpa）1'")
    private String workingPressureTo;

    @Column(columnDefinition = "varchar(255) comment '耐压实验（Mpa）1'")
    private String withstandVoltageTo;

    @Column(columnDefinition = "varchar(255) comment '耐压实验（Mpa）2'")
    private String withstandVoltageTo2;

    /**
     * 其他实验
     */
    @Column(columnDefinition = "varchar(255) comment '其他实验'")
    private String otherExperimentsTo;

    @Column(columnDefinition = "varchar(255) comment '其他实验2'")
    private String otherExperimentsTo2;

    @Column(columnDefinition = "varchar(255) comment '左端螺纹1'")
    private String leftThreadTo;

    @Column(columnDefinition = "varchar(255) comment '右端螺纹1'")
    private String rightThreadTo;

    @ApiModelProperty("图号")
    @Column(columnDefinition = "varchar(255) comment '图号'")
    private String drawingNo;

    /**
     * 不合格处理流程单单号
     */
    @Column(columnDefinition = "varchar(255) comment '处理流程单号'")
    private String handleCode;


    @Column(columnDefinition = "datetime comment '总装完成日期'")
    private Date finishTime;

    //--------------------------------------------试验相关-------------------------------------------------------
    @Column(columnDefinition = "varchar(255) comment '编织层外径'")
    private String weaveDiameter;

    @Column(columnDefinition = "varchar(255) comment '编织层行程'")
    private String weaveTravel;

    @Column(columnDefinition = "varchar(255) comment '扣压尺寸左'")
    private String withholdSizeLeft;

    @Column(columnDefinition = "varchar(255) comment '扣压尺寸右'")
    private String withholdSizeRight;

}

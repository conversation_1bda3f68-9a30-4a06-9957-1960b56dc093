package com.hvisions.productBoard.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 成品不合格审理单
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("sys_qms.hy_qm_finished_nonconformity_review")
public class HyQmFinishedNonconformityReview extends SysBase {
    /**
    * 批次
    */
    private String batchNum;

    /**
    * 图号
    */
    private String drawingNo;

    /**
    * 检验员
    */
    private String examination;

    /**
    * 发起时间
    */
    private Date initiateTime;

    /**
    * 不合格品数量
    */
    private Integer nonconformityCount;

    /**
    * 不合格品分级
    */
    private Integer nonconformityGrade;

    /**
    * 审理单号
    */
    private String orderCode;

    /**
    * 产品编码
    */
    private String productCode;

    /**
    * 规格
    */
    private String productEigenvalue;

    /**
    * 型号
    */
    private String productModel;

    /**
    * 产品名称
    */
    private String productName;

    /**
    * 检验阶段
    */
    private Integer qualityClass;

    /**
    * 审评状态
    */
    private Integer qualityState;

    /**
    * 审评时间
    */
    private Date qualityTime;

    /**
    * 审评类型
    */
    private String qualityType;

    /**
    * 备注
    */
    private String remark;

    /**
    * 销售订单号
    */
    private String saleOrderCode;

    /**
    * 验收提交单号
    */
    private String submitOrderCode;

    /**
    * 本批数量
    */
    private Integer totalCount;

    /**
    * 生产任务单号
    */
    private String workOrderCode;

    /**
    * 原始记录单
    */
    private String workSubOrderCode;

    /**
    * 产品提交单产品名称
    */
    private String submitProductName;

    /**
    * 打印编号
    */
    private String printNo;

    /**
    * 版本
    */
    private String version;
}
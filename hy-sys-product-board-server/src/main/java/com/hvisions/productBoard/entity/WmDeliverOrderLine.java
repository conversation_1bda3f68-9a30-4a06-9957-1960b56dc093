package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_wms.hy_wms_deliver_order_line")
public class WmDeliverOrderLine extends SysBase {

    /**
     * 移库单编码
     */
    private String deliverOrderCode;

    /**
     * 业务类型 DeliverOrderTypeEnum
     */
    private Integer orderType;

    /**
     * 业务单号
     */
    private String orderCode;

    /**
     * 状态 DeliverOrderSubmitStateEnum 提交SAP状态
     */
    private Integer submitState;

    /**
     * 发出方状态 DeliverOrderStateEnum
     */
    private Integer fromState;

    /**
     * 接受方状态 DeliverOrderStateEnum
     */
    private Integer targetState;

    /**
     * 物料编号
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;

    private String materialDesc;
    /**
     * 图号
     */
    private String drawingNum;

    /**
     * 物料组
     */
    private String materialGroup;
    private String materialGroupName;

    /**
     * 物料类型
     */
    private String materialType;
    private String materialTypeName;

    /**
     * 指定批次
     */
    private String batchNum;

    /**
     * 物料批次
     */
    private String materialBatchNum;

    /**
     * 原料辅批
     */
    private String minorMaterialBatchNum;

    /**
     * 需求量
     */
    private BigDecimal needQuantity = BigDecimal.ZERO;
    /**
     * 计划领料数量
     */
    private BigDecimal planQuantity = BigDecimal.ZERO;
    /**
     * 发出数量
     */
    private BigDecimal fromQuantity = BigDecimal.ZERO;

    /**
     * 接受数量
     */
    private BigDecimal targetQuantity = BigDecimal.ZERO;
    /**
     * 已提交数量
     */
    private BigDecimal submitQuantity = BigDecimal.ZERO;
    /**
     * 补领数量
     */
    private BigDecimal supplementQuantity = BigDecimal.ZERO;
    /**
     * 单位
     */
    private String unit;
    private String unitName;
    /**
     * 领用日期
     */
    private LocalDateTime receiveTime;
    /**
     * 过账日期
     */
    private LocalDateTime postingTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 领料部门
     */
    private String department;

    /**
     * 来源库位
     */
    private String fromLocationCode;
    private String fromLocation;

    /**
     * 目标库位
     */
    private String targetLocationCode;
    private String targetLocation;

    /**
     * 物料规格
     */
    private String materialEigenvalue;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 产品规格
     */
    private String productEigenvalue;


    /**
     * 车间退料 类别
     */
    private String category;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 业务类型 DeliverOperationEnum
     */
    private Integer operation;

    /**
     * 是否允许超量：0-false, 1-true
     */
    private Boolean isExcess;


    /**
     * 业务单号类型
     */
    private Integer bizOrderType;
    private String bizOrderTypeName;

    /**
     * 预留单号
     */
    private String rsnum;
    /**
     * 预留行号
     */
    private String rspos;

}

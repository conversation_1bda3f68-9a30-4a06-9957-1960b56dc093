package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 增强生产任务单
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_pms.hy_pm_enhance_work_order")
public class HyPmEnhanceWorkOrder extends SysBase {
    /**
     * 实际结束时间
     */
    private Date actualEndTime;

    /**
     * 实际开始时间
     */
    private Date actualStartTime;

    /**
     * BOM 编码
     */
    private String bomCode;

    /**
     * BOM ID
     */
    private Integer bomId;

    /**
     * BOM 名称
     */
    private String bomName;

    /**
     * BOM 版本
     */
    private String bomVersion;

    /**
     * 工单编码
     */
    private String code;

    /**
     * 完成步骤
     */
    private Integer completeStep;

    /**
     * 制单人
     */
    private String creatorUserName;

    /**
     * 领料状态
     */
    private Integer deliverState;

    /**
     * 增强形式
     */
    private String enhanceModality;

    private BigDecimal finishedQuantity;

    /**
     * 内管颜色
     */
    private String innerPipeColor;

    /**
     * 批次
     */
    private String materialBatchNum;

    /**
     * 产品编码
     */
    private String materialCode;

    /**
     * 产品简称
     */
    private String materialDesc;

    /**
     * 产品规格
     */
    private String materialEigenvalue;

    /**
     * 产品id
     */
    private Integer materialId;

    /**
     * 产品名称
     */
    private String materialName;

    /**
     * 材料路线ID
     */
    private Integer materialRouteId;

    /**
     * 计划结束时间
     */
    private Date planEndTime;

    private BigDecimal planQuantity;

    /**
     * 计划开始时间
     */
    private Date planStartTime;

    /**
     * 打印次数
     */
    private Integer printCount;

    /**
     * 产品分组
     */
    private String productGroup;

    /**
     * 产品分组编码
     */
    private String productGroupCode;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品类型编码
     */
    private String productTypeCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 树脂原料牌号
     */
    private String resinRawMaterialName;

    /**
     * 工艺路线编码
     */
    private String routeCode;

    /**
     * 工艺路线ID
     */
    private Integer routeId;

    /**
     * 工艺路线名称
     */
    private String routeName;

    /**
     * 工艺路线更新时间
     */
    private Date routeUpdateTime;

    /**
     * 工艺路线版本
     */
    private String routeVersion;

    /**
     * 生产状态
     */
    private Integer state;

    /**
     * 钢丝型号
     */
    private String steelWireName;

    /**
     * 生产类型
     */
    private Integer type;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 单位
     */
    private String unitSymbol;

    /**
     * 用途
     */
    private String useWay;

    /**
     * 作业指导书
     */
    private String workingInstruction;

    /**
     * 内管管号
     */
    private String pipeNo;

    /**
     * 产出凭证
     */
    private String outputMblnr;

    /**
     * 产出响应信息
     */
    private String outputMsg;

    /**
     * SAP生产订单号
     */
    private String sapCode;

    /**
     * 钢丝批号
     */
    private String wireBatch;

    /**
     * 消耗提交状态
     */
    private Integer consumeSubmitState;

    /**
     * 钢丝批次指定列表信息存储
     */
    private String wireBatchJson;

    /**
     * 编织管根数
     */
    private Integer pipeNum;
}
package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 备货需求单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_sale.hy_sa_sale_order")
public class SaSaleOrder extends SysBase {

    /**
     * 制单人
     */
    private String creatorName;

    /**
     * 父备货需求号
     */
    private String parentCode;

    /**
     * 备货需求号
     */
    private String code;

    /**
     * 对方合同号
     */
    private String contractCode;

    /**
     * 购货单位
     */
    private Integer customerId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户Sap编码
     */
    private String customerSapCode;

    /**
     * 客户简称
     */
    private String customerName;

    /**
     * 客户全称
     */
    private String customerFullName;

    /**
     * 购货单位名称
     */
    private String customerCompany;

    /**
     * 地址
     */
    private String address;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 军种
     */
    private String armyType;

    /**
     * 机型
     */
    private String machineType;

    /**
     * 预计完成日期
     */
    private LocalDate planFinishDate;

    /**
     * 警告日期
     */
    private LocalDate warningDate;

    /**
     * 异常日期
     */
    private LocalDate exceptionDate;

    /**
     * 关闭状态
     */
    private Integer closeState;

    /**
     * 执行状态
     */
    private Integer executeState;

    /**
     * 合同状态
     */
    private Integer contractState;

    /**
     * 下派数量
     */
    private BigDecimal dispatchCount;

    /**
     * 下派状态
     */
    private Integer dispatchState;

    /**
     * 计划总量
     */
    private BigDecimal planCount;

    /**
     * 生产完成总量
     */
    private BigDecimal productionCount;

    /**
     * 质检完成总量
     */
    private BigDecimal qualityCount;

    /**
     * 军检完成总量
     */
    private BigDecimal militaryQualityCount;

    /**
     * 入库完成总量
     */
    private BigDecimal wareCount;

    /**
     * 发货完成总量
     */
    private BigDecimal deliverCount;

    /**
     * 开票完成总量
     */
    private BigDecimal invoiceCount;

    /**
     * 生产取消总量
     */
    private BigDecimal productionCancelCount;

    /**
     * 运输方式
     */
    private String transport;

    /**
     * 费用负担
     */
    private String freight;

    /**
     * 发货状态
     * 0: 未发货
     * 10: 部分发货
     * 20: 全部发货
     * 30: 部分完成
     * 40: 发货完成
     */
    private Integer shippingState;

    /**
     * 备注
     */
    private String remark;

    /**
     * 部门
     */
    private String department;

    /**
     * 预计发货数量
     */
    private BigDecimal predictShippingCount;

    /**
     * 可发货数量
     */
    private BigDecimal shipmentCount;

    /**
     * 物料分组类型（三部）
     */
    private String materialGroupTypeCode;
    /**
     * 物料分组类型（三部）
     */
    private String materialGroupTypeName;

    /**
     * 客户验收
     */
    private Boolean isCustomerQualify;

    /**
     * 待检量
     */
    private BigDecimal waitQualityCount;
    /**
     * 待入库量
     */
    private BigDecimal waitWareCount;

    /**
     * 用途
     */
    private Integer purpose;

}

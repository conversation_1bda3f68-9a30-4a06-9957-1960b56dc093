package com.hvisions.productBoard.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 包装任务单明细
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("sys_pms.hy_pm_pkg_order_line")
public class HyPmPkgOrderLine extends SysBase {
    /**
    * 批次
    */
    private String batchNo;

    /**
    * 包装任务单号
    */
    private String code;

    /**
    * 产品编码
    */
    private String materialCode;

    /**
    * 规格
    */
    private String materialEigenvalue;

    /**
    * 产品名称
    */
    private String materialName;

    /**
    * 产品简称
    */
    private String materialNameDesc;

    /**
    * 备注
    */
    private String memo;

    /**
    * 型号
    */
    private String productModel;

    /**
    * 总数量
    */
    private Integer quantity;

    /**
    * 销售批次
    */
    private String saleBatchNo;

    /**
    * 销售订单号
    */
    private String saleOrderCode;

    /**
    * SAP生产订单号
    */
    private String sapCode;

    /**
    * 验收提交单号
    */
    private String submitOrderCode;

    /**
    * 每包多少根管子
    */
    private Integer unitQuantity;

    /**
    * 原始记录单
    */
    private String workSubOrderCode;

    private BigDecimal unitPrice;
}
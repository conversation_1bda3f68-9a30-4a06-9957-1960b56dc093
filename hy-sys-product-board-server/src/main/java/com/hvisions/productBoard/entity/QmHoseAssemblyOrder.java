package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 软管组件检验单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_qms.hy_qm_hose_assembly_order")
public class QmHoseAssemblyOrder extends SysBase {

    /**
     * 是否创建完成不合格流程单子, 0-否，1-是,默认值0
     */
    private Integer unqualified = 0;


    /**
     * 软管检验单号
     */
    private String orderCode;
    /**
     * 图纸ID
     */
    private Integer drawingFileId;
    /**
     * 产品型号
     */
    private String productModel;
    /**
     * 物料ID
     */
    private Integer materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料代码
     */
    private String materialCode;


    /**
     * 验收提交单号
     */
    private String submitOrderCode;
    /**
     * 质检类型 QualityTypeEnum{多段式，普通}
     */
    private Integer qualityType;
    /**
     * 对方合同号
     */
    private String sideContract;
    /**
     * 订货单位
     */
    private String supplierName;
    /**
     * 检验状态 HoseQualityStateEnum {新建、待复核、试验完成、待质检、待审理、质检完成、全部}
     * 100 待确认
     * 0 新建
     * 5 待复核
     * 10 试验完成
     * 15 待质检
     * 20 待审理
     * 25 质检完成
     * 40 未激活
     * 50 关闭
     */
    private Integer qualityState;

    /**
     * 关闭前检验状态
     */
    private Integer qualityStateBefore;


    /**
     * 部门状态 HoseDepartmentStateEnum
     */
    private Integer departmentState;

    /**
     * 生产数量
     */
    private Integer totalCount;
    /**
     *试验合格数 默认值0
     */
    private Integer trialPassCount = 0;

    /**
     * 试验不合格数
     */
    private Integer trialUnPassCount = 0;
    /**
     * 质检合格数 默认值0
     */
    private Integer qualityPassCount = 0;

    /**
     * 放行数量
     */
    private Integer releaseQty = 0;
    /**
     * 产品规格
     */
    private String productEigenvalue;
    /**
     * 批次
     */
    private String batchNum;

    /**
     * 销售订单号
     */
    private String saleOrderCode;

    /**
     * 销售订单明细ID
     */
    private Integer saleOrderLineId;
    /**
     * 生产任务单号
     */
    private String workOrderCode;

    /**
     * 原始记录单
     */
    private String workSubOrderCode;

    /**
     * 父原始记录单号
     */
    private String parentWorkSubOrderCode;

    /**
     * 军种
     */
    private String armType;
    /**
     * 图号(工艺名称)
     */
    private String drawNum;
    /**
     * 机型
     */
    private String machineType;
    /**
     * 是否提前检验
     */
    private Integer earlierCheckout;

    /**
     * 是否提前检验(隐藏)
     */
    private Integer hideEarlierCheckout;


    /**
     * 是否打印合格证
     */
    private Integer certificate = 0;
    /**
     * 生产日期
     */
    private LocalDateTime produceTime;
    /**
     * 试验完成日期
     */
    private LocalDateTime trialAccomplishTime;

    /**
     * 试验完成日期2
     */
    private LocalDateTime trialAccomplishTime2;

    /**
     * 备注
     */
    private String remark;

    /**
     * 备注
     */
    private String remark2;

    /**
     * 存放位置
     */
    private String storageLocation;

    /**
     * 直尺
     */
    private String ruler;
    /**
     * 称
     */
    private String hoseCall;
    /**
     * 止通规
     */
    private String gauge;
    /**
     * 卡尺
     */
    private String callipers;
    /**
     * 其他试验
     */
    private String otherTests;
    /**
     * 总装人员
     */
    private Integer finalUserId;
    /**
     * 总装人员
     */
    private String finalUserName;
    /**
     * 总装人员签字日期
     */
    private LocalDateTime finalUserTime;

    /**
     * 总装人员2
     */
    private Integer finalUserId2;
    /**
     * 总装人员2
     */
    private String finalUserName2;

    /**
     * 总装人员签字日期2
     */
    private LocalDateTime finalUserTime2;

    /**
     * 干燥时间
     */
    private LocalDateTime dryingTime;
    /**
     * 试验人员
     */
    private Integer trialUserId;
    /**
     * 试验人员
     */
    private String trialUserName;

    /**
     * 试验人员2
     */
    private Integer trialUserId2;
    /**
     * 试验人员2
     */
    private String trialUserName2;

    /**
     * 试验日期
     */
    private LocalDateTime trialTime;

    /**
     * 试验日期2
     */
    private LocalDateTime trialTime2;

    /**
     * 质检人员
     */
    private Integer qualityUserId;
    /**
     * 质检人员
     */
    private String qualityUserName;
    /**
     * 质检日期
     */
    private LocalDateTime qualityTime;

    /**
     * 提前质检人员
     */
    private Integer earlyQualityUserId;
    /**
     * 提前质检人员
     */
    private String earlyQualityUserName;
    /**
     * 提前质检日期
     */
    private LocalDateTime earlyQualityTime;

    /**
     * 客户代表
     */
    private Integer customerUserId;
    /**
     * 客户代表
     */
    private String customerUserName;
    /**
     * 客户代表日期
     */
    private LocalDateTime customerTime;


    /**
     * 是否提交过 false-否，ture-是
     */
    private Boolean isSave = false;

    /**
     * 是否激活 false-否，ture-是，默认激活 (提前检验 退回到待质检变为未激活)
     */
    private Boolean isActivation = true;

    /**
     * 是否被退回过, false-否，ture-是
     */
    private Boolean isReturn = false;

    /**
     * 是否被拆分过, false-否，ture-是
     */
    private Boolean isSplit = false;

    /**
     * 是否创建过不合格审理单 false-否，ture-是
     */
    private Boolean isCreateReviewOrder = false;

    /**
     * 是否质检提交过, false-否，ture-是
     */
    private Boolean isQuSubmit = false;


    /**
     * 图纸版本
     */
    private String drawingVersion;
    /**
     * 最新图纸版本
     */
    private String drawingVersionLatest;

    /**
     * 最新图纸更新时间
     */
    private LocalDateTime drawingUpdateDateTime;
    /**
     * 图纸状态
     */
    private Integer drawingState;
    /**
     * 产品数据库id
     */
    private Integer materialRouteId;

    /**
     * 产品数据库编码
     */
    private String materialRouteCode;

    /**
     * 科研任务单号
     */
    private String researchOrderCode;

    /**
     * 科研任务单id
     */
    private Integer researchOrderLineId;

    /**
     * 单据类型
     */
    private Integer orderType;

    /**
     * 选择硫化类型的次数
     */
    private Integer orderTypeTimes;

    /**
     * 是否特殊软管
     */
    private Boolean isSpecial = Boolean.FALSE;

    /**
     * 是否紧急
     */
    private Boolean isUrgency = Boolean.FALSE;

    /**
     * 是否试验
     */
    private Boolean isTest = Boolean.FALSE;

    /**
     * 检验次数
     */
    private Integer testTimes = 1;

}

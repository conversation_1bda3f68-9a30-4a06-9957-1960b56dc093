package com.hvisions.productBoard.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品验收提交
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("sys_qms.hy_qm_product_check_submit")
public class HyQmProductCheckSubmit extends SysBase {
    /**
    * 军种
    */
    private String armType;

    /**
    * 包装任务单ID
    */
    private Integer bomId;

    /**
    * 合同号
    */
    private String contract;

    private Boolean flag;

    /**
    * 送检量
    */
    private Integer inspectionQuantity;

    /**
    * 检验员
    */
    private String inspector;

    /**
    * 软管检验单号
    */
    private String orderCode;

    /**
    * 合格率
    */
    private BigDecimal passRate;

    /**
    * 打印次数
    */
    private Integer printTimes;

    /**
    * 打印类型
    */
    private String printType;

    /**
    * 生产日期
    */
    private Date produceTime;

    /**
    * 产品编码
    */
    private String productCode;

    /**
    * 产品名称
    */
    private String productName;

    /**
    * 合格量
    */
    private Integer qualifiedQuantity;

    /**
    * 质检结果
    */
    private Integer quality;

    /**
    * 质检结论
    */
    private String qualityConclusion;

    /**
    * 检验状态
    */
    private Integer qualityState;

    /**
    * 检验日期
    */
    private Date qualityTime;

    /**
    * 备注
    */
    private String remark;

    /**
    * 销售订单号
    */
    private String saleOrderCode;

    /**
    * 对方合同号
    */
    private String sideContract;

    /**
    * 存放位置
    */
    private String storageLocation;

    /**
    * 验收提交单号
    */
    private String submitOrderCode;

    /**
    * 提交数量
    */
    private Integer submitQuantity;

    /**
    * 提交日期
    */
    private Date submitTime;

    /**
    * 监督协议号
    */
    private String supervisionOrder;

    /**
    * 订货单位
    */
    private String supplierName;

    /**
    * 技术要求
    */
    private String technicalAsk;

    /**
    * 审理单号
    */
    private String tryCode;

    /**
    * 拆分标记
    */
    private Boolean splitTag;

    /**
    * 科研任务单号
    */
    private String researchOrderCode;

    /**
    * 检验日期
    */
    private String inspectDateSummary;

    /**
    * 产品型号
    */
    private String productModel;

    /**
    * 是否在301创建状态
    */
    private Integer registState;

    /**
    * 检验次数
    */
    private Integer testTimes;
}
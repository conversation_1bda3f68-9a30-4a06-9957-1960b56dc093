package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 批次库存表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_wms.hy_wms_material_batch_stocks")
public class WmsMaterialBatchStocks extends SysBase {
    /**
     * 物料类型
     */
    private String materialTypeCode;
    /**
     * 物料类型名称
     */
    private String materialTypeName;
    /**
     * 物料分组
     */
    private String materialGroupCode;
    /**
     * 物料分组名称
     */
    private String materialGroupName;
    /**
     * 物料ID
     */
    private Integer materialId;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料规格
     */
    private String materialEigenvalue;
    /**
     * 图号
     */
    private String drawingNum;
    /**
     * 物料简称
     */
    private String materialDescribe;

    // 原料的实际批次 成品的销售批次
    /**
     * 物料批次
     */
    private String materialBatchNum;
    /**
     * 原材料采购批次
     */
    private String purchaseBatchNum;
    /**
     * 成品生产批次
     */
    private String saleBatch;
    /**
     * 供应商物料批次
     */
    private String supplierMaterialBatchNum;
    /**
     * 入库时间
     */
    private LocalDateTime shiftDateTime;
    /**
     * 生产日期
     */
    private LocalDateTime productDateTime;
    /**
     * 过期日期
     */
    private LocalDateTime effectDateTime;
    /**
     * 过期状态
     */
    private Integer expireState;
    /**
     * 质检状态
     */
    private Integer qualityState;
    /**
     * 试验状态
     */
    private Integer testState;
    /**
     * 物料单位
     */
    private String materialUnit;
    /**
     * 物料单位中文
     */
    private String materialUnitName;
    /**
     * 在库数量
     */
    private BigDecimal quantity;
    /**
     * 可用数量
     */
    private BigDecimal useableQuantity;
    /**
     * 发货在途数量
     */
    private BigDecimal shippingQuantity;
    /**
     * 在检数量
     */
    private BigDecimal qualityQuantity;
    /**
     * 冻结数量
     */
    private BigDecimal frozenQuantity;
    /**
     * 领料在途量
     */
    private BigDecimal deliveryQuantity;
    /**
     * 领料单锁定量
     */
    private BigDecimal deliverLockQuantity;
    /**
     * 仓库编码
     */
    private String wareLocationCode;
    /**
     * 仓库名称
     */
    private String wareLocationName;
    /**
     * 仓库类型编码
     */
    private String wareLocationClassCode;
    /**
     * 仓库类型名称
     */
    private String wareLocationClassName;
}
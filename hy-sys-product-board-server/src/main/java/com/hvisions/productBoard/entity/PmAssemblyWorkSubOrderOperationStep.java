package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_pms.hy_pm_work_sub_order_operation_step")
public class PmAssemblyWorkSubOrderOperationStep extends SysBase {

    /**
     * 任务单号
     */
    private String orderCode;

    /**
     * 原始记录单id
     */
    private Integer workSubOrderId;

    /**
     * 原始记录单编码
     */
    private String workSubOrderCode;

    /**
     * 原始记录单序号
     */
    private Integer workSubOrderSeq;

    /**
     * 原始记录单序号
     */
    private Integer seq;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 落料 操作人
     */
    private Integer blankingOperatorId;

    /**
     * 落料 操作人签名
     */
    private Integer blankingOperatorSignature;

    /**
     * 落料 签字时间
     */
    private LocalDateTime blankingSignDateTime;

    /**
     * 落料 操作人编码
     */
    private String blankingOperatorCode;

    /**
     * 落料 操作人名称
     */
    private String blankingOperatorName;

    /**
     * 落料 确认内容
     */
    private String blankingConfirmContent;

    /**
     * 落料 确认情况
     */
    private String blankingConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 内衬与接头连接 操作人
     */
    private Integer connectingOperatorId;

    /**
     * 内衬与接头连接 操作人签名
     */
    private Integer connectingOperatorSignature;

    /**
     * 内衬与接头连接 签字时间
     */
    private LocalDateTime connectingSignDateTime;

    /**
     * 内衬与接头连接 操作人编码
     */
    private String connectingOperatorCode;

    /**
     * 内衬与接头连接 操作人名称
     */
    private String connectingOperatorName;

    /**
     * 内衬与接头连接 确认内容
     */
    private String connectingConfirmContent;

    /**
     * 内衬与接头连接 确认情况
     */
    private String connectingConfirmState;

    ////////////////////////////////////////////////////////////////////////////////


    /**
     * 清洁 操作人
     */
    private Integer cleaningOperatorId;

    /**
     * 清洁 操作人签名
     */
    private Integer cleaningOperatorSignature;

    /**
     * 清洁 签字时间
     */
    private LocalDateTime cleaningSignDateTime;

    /**
     * 清洁 操作人编码
     */
    private String cleaningOperatorCode;

    /**
     * 清洁 操作人名称
     */
    private String cleaningOperatorName;

    /**
     * 清洁 确认内容
     */
    private String cleaningConfirmContent;

    /**
     * 清洁 确认情况
     */
    private String cleaningConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 内管切割 操作人
     */
    private Integer innerTubeCuttingOperatorId;

    /**
     * 内管切割 操作人签名
     */
    private Integer innerTubeCuttingOperatorSignature;

    /**
     * 内管切割 签字时间
     */
    private LocalDateTime innerTubeCuttingSignDateTime;

    /**
     * 内管切割 操作人编码
     */
    private String innerTubeCuttingOperatorCode;

    /**
     * 内管切割 操作人名称
     */
    private String innerTubeCuttingOperatorName;

    /**
     * 内管切割 确认内容
     */
    private String innerTubeCuttingConfirmContent;

    /**
     * 内管切割 确认情况
     */
    private String innerTubeCuttingConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 接头装配 操作人
     */
    private Integer connectorAssemblingOperatorId;

    /**
     * 接头装配 操作人签名
     */
    private Integer connectorAssemblingOperatorSignature;

    /**
     * 接头装配 签字时间
     */
    private LocalDateTime connectorAssemblingSignDateTime;

    /**
     * 接头装配 操作人编码
     */
    private String connectorAssemblingOperatorCode;

    /**
     * 接头装配 操作人名称
     */
    private String connectorAssemblingOperatorName;

    /**
     * 接头装配 确认内容
     */
    private String connectorAssemblingConfirmContent;

    /**
     * 接头装配 确认情况
     */
    private String connectorAssemblingConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 登记 操作人
     */
    private Integer registerOperatorId;

    /**
     * 登记 操作人签名
     */
    private Integer registerOperatorSignature;

    /**
     * 登记 签字时间
     */
    private LocalDateTime registerSignDateTime;

    /**
     * 登记 操作人编码
     */
    private String registerOperatorCode;

    /**
     * 登记 操作人名称
     */
    private String registerOperatorName;

    /**
     * 登记 确认内容
     */
    private String registerConfirmContent;

    /**
     * 登记 确认情况
     */
    private String registerConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 扣压关键工序 操作人
     */
    private Integer crimpingOperatorId;

    /**
     * 扣压关键工序 操作人签名
     */
    private Integer crimpingOperatorSignature;

    /**
     * 扣压关键工序 签字时间
     */
    private LocalDateTime crimpingSignDateTime;

    /**
     * 扣压关键工序 操作人编码
     */
    private String crimpingOperatorCode;

    /**
     * 扣压关键工序 操作人名称
     */
    private String crimpingOperatorName;

    /**
     * 扣压关键工序 确认内容
     */
    private String crimpingConfirmContent;

    /**
     * 扣压关键工序 确认情况
     */
    private String crimpingConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 收缩套 操作人
     */
    private Integer shrinkSleeveOperatorId;

    /**
     * 收缩套 操作人签名
     */
    private Integer shrinkSleeveOperatorSignature;

    /**
     * 收缩套 签字时间
     */
    private LocalDateTime shrinkSleeveSignDateTime;

    /**
     * 收缩套 操作人编码
     */
    private String shrinkSleeveOperatorCode;

    /**
     * 收缩套 操作人名称
     */
    private String shrinkSleeveOperatorName;

    /**
     * 收缩套 确认内容
     */
    private String shrinkSleeveConfirmContent;

    /**
     * 收缩套 确认情况
     */
    private String shrinkSleeveConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 防护套 操作人
     */
    private Integer protectionSleeveOperatorId;

    /**
     * 防护套 操作人签名
     */
    private Integer protectionSleeveOperatorSignature;

    /**
     * 防护套 签字时间
     */
    private LocalDateTime protectionSleeveSignDateTime;

    /**
     * 防护套 操作人编码
     */
    private String protectionSleeveOperatorCode;

    /**
     * 防护套 操作人名称
     */
    private String protectionSleeveOperatorName;

    /**
     * 防护套 确认内容
     */
    private String protectionSleeveConfirmContent;

    /**
     * 防护套 确认情况
     */
    private String protectionSleeveConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 螺母装配 操作人
     */
    private Integer nutAssemblingOperatorId;

    /**
     * 螺母装配 操作人签名
     */
    private Integer nutAssemblingOperatorSignature;

    /**
     * 螺母装配 签字时间
     */
    private LocalDateTime nutAssemblingSignDateTime;

    /**
     * 螺母装配 操作人编码
     */
    private String nutAssemblingOperatorCode;

    /**
     * 螺母装配 操作人名称
     */
    private String nutAssemblingOperatorName;

    /**
     * 螺母装配 确认内容
     */
    private String nutAssemblingConfirmContent;

    /**
     * 螺母装配 确认情况
     */
    private String nutAssemblingConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 标牌制作 操作人
     */
    private Integer signMakingOperatorId;

    /**
     * 标牌制作 操作人签名
     */
    private Integer signMakingOperatorSignature;

    /**
     * 标牌制作 签字时间
     */
    private LocalDateTime signMakingSignDateTime;

    /**
     * 标牌制作 操作人编码
     */
    private String signMakingOperatorCode;

    /**
     * 标牌制作 操作人名称
     */
    private String signMakingOperatorName;

    /**
     * 标牌制作 确认内容
     */
    private String signMakingConfirmContent;

    /**
     * 标牌制作 确认情况
     */
    private String signMakingConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 自检 操作人
     */
    private Integer selfInspectionOperatorId;

    /**
     * 自检 操作人签名
     */
    private Integer selfInspectionOperatorSignature;

    /**
     * 自检 签字时间
     */
    private LocalDateTime selfInspectionSignDateTime;

    /**
     * 自检 操作人编码
     */
    private String selfInspectionOperatorCode;

    /**
     * 自检 操作人名称
     */
    private String selfInspectionOperatorName;

    /**
     * 自检 确认内容
     */
    private String selfInspectionConfirmContent;

    /**
     * 自检 确认情况
     */
    private String selfInspectionConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 软管组件复验 操作人
     */
    private Integer reInspectionOperatorId;

    /**
     * 软管组件复验 操作人签名
     */
    private Integer reInspectionOperatorSignature;

    /**
     * 软管组件复验 签字时间
     */
    private LocalDateTime reInspectionSignDateTime;

    /**
     * 软管组件复验 操作人编码
     */
    private String reInspectionOperatorCode;

    /**
     * 软管组件复验 操作人名称
     */
    private String reInspectionOperatorName;

    /**
     * 软管组件复验 确认内容
     */
    private String reInspectionConfirmContent;

    /**
     * 软管组件复验 确认情况
     */
    private String reInspectionConfirmState;

    ////////////////////////////////////////////////////////////////////////////////

    /**
     * 根数
     */
    private BigDecimal quantity;

}

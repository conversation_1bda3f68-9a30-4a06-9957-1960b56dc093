package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 物料
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_hiper_base.hv_bm_material")
public class BmMaterial extends SysBase {

    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料简称
     */
    private String materialDesc;

    /**
     * 物料类型
     */
    private Integer materialType;
    private String materialTypeCode;
    /**
     * 物料分组id
     */
    private Integer materialGroup;
    /**
     * 物料分组编码
     */
    private String materialGroupCode;
    /**
     * 物料分组大类
     */
    private String materialGroupCategory;

    /**
     * 是否需要检验
     */
    private boolean needInspection;
    /**
     * 物料规格
     */
    private String specifications;
    /**
     * 单位
     */
    private Integer uom;

    /**
     * 图号
     */
    private String figureNumber;

    /**
     * 型号
     */
    private String productModel;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 含税单价
     */
    private BigDecimal priceTax;
    /***
     * 特征值
     */
    private String eigenvalue;

    /**
     * 图片ID
     */
    private Integer photoId;

    /**
     * 部门
     */
    private String department;

}

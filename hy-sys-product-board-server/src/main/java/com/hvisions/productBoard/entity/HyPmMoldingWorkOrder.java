package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 成型生产任务单
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_pms.hy_pm_molding_work_order")
public class HyPmMoldingWorkOrder extends SysBase {
    /**
     * 实际结束时间
     */
    private Date actualEndTime;

    /**
     * 实际开始时间
     */
    private Date actualStartTime;

    /**
     * BOM 编码
     */
    private String bomCode;

    /**
     * BOM ID
     */
    private Integer bomId;

    /**
     * BOM 名称
     */
    private String bomName;

    /**
     * BOM 版本
     */
    private String bomVersion;

    /**
     * 工单编码
     */
    private String code;

    /**
     * 制单人
     */
    private String creatorUserName;

    /**
     * 领料状态
     */
    private Integer deliverState;

    private BigDecimal finishedQuantity;

    private BigDecimal submittedQuantity;

    /**
     * 内管颜色
     */
    private String innerPipeColor;

    /**
     * 产品批次
     */
    private String materialBatchNum;

    /**
     * 产品编码
     */
    private String materialCode;

    /**
     * 产品规格
     */
    private String materialEigenvalue;

    /**
     * 产品id
     */
    private Integer materialId;

    /**
     * 产品名称
     */
    private String materialName;

    /**
     * 材料路线ID
     */
    private Integer materialRouteId;

    private BigDecimal planBlankQuantity;

    /**
     * 计划结束时间
     */
    private Date planEndTime;

    private BigDecimal planQuantity;

    /**
     * 计划开始时间
     */
    private Date planStartTime;

    /**
     * 产品分组
     */
    private String productGroup;

    /**
     * 产品分组编码
     */
    private String productGroupCode;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品类型编码
     */
    private String productTypeCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 树脂原料牌号
     */
    private String resinRawMaterialName;

    /**
     * 工艺路线编码
     */
    private String routeCode;

    /**
     * 工艺路线ID
     */
    private Integer routeId;

    /**
     * 工艺路线名称
     */
    private String routeName;

    /**
     * 工艺路线更新时间
     */
    private Date routeUpdateTime;

    /**
     * 工艺路线版本
     */
    private String routeVersion;

    /**
     * 生产状态
     */
    private Integer state;

    /**
     * 生产类型
     */
    private Integer type;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 单位
     */
    private String unitSymbol;

    /**
     * 用途
     */
    private String useWay;

    /**
     * 内管颜色
     */
    private String color;

    /**
     * 制单人
     */
    private String creatorName;

    /**
     * 领料状态
     */
    private String deliverStateName;

    /**
     * 执行参数编码
     */
    private String executeParam;

    /**
     * 产品数据库编码
     */
    private String materialRouteCode;

    /**
     * 产品数据库名称
     */
    private String materialRouteName;

    /**
     * 产品数据库更新时间
     */
    private Date materialRouteUpdateTime;

    /**
     * 产品数据库版本
     */
    private String materialRouteVersion;

    /**
     * 产品批次
     */
    private String productBatchNumber;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品规格
     */
    private String productEigenvalue;

    /**
     * 产品分组
     */
    private String productGroupName;

    /**
     * 产品
     */
    private Integer productId;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品类型
     */
    private String productTypeName;

    /**
     * 树脂原料牌号
     */
    private String resinName;

    /**
     * 工艺规程
     */
    private String routeRule;

    /**
     * 生产状态
     */
    private String stateName;

    /**
     * 生产类型
     */
    private String typeName;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 用途
     */
    private String useWayName;

    /**
     * 作业指导书
     */
    private String workGuideBook;

    /**
     * 图号
     */
    private String drawingNum;

    /**
     * 产出凭证
     */
    private String outputMblnr;

    /**
     * 产出响应信息
     */
    private String outputMsg;

    /**
     * SAP生产订单号
     */
    private String sapCode;

    /**
     * 消耗提交状态
     */
    private Integer consumeSubmitState;

    /**
     * 已生产重量
     */
    private BigDecimal submittedWeight;
}
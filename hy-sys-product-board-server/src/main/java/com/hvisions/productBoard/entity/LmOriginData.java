package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * LIMS 原始记录单
 */
@Data
@TableName(value = "sys_lims2.origin_data")
public class LmOriginData {

    private Long id;

    /**
     * 委托id
     */
    private Long contractId;

    /**
     * 合同检测记录
     */
    private Long contractInspectionId;

    /**
     * 原始记录名称
     */
    private String originName;

    /**
     * 原始记录编号
     */
    private String originDataNumber;

    /**
     * 样品编号
     */
    private String sampleNumber;

    /**
     * 样品名称
     */
    private String sampleName;

    /**
     * 原始记录自动编号前缀
     */
    private String originDataAutoNumPrefix;

    /**
     * 原始记录自动编号状态
     */
    private int originDatatAutoNumStatus;

    /**
     * 原始记录自动编号
     */
    private int originDataAutoNum;

    /**
     * 原始记录参数
     */
    private String originPara;

    /**
     * 原始记录数据，用来写入原始记录单数据 json 格式
     */
    private String originData;

    /**
     * 原始记录格式
     */
    private String originFormat;

    /**
     * 原始记录编辑格式
     */
    private String originEditFormat;

    /**
     * 原始记录HTML解析出来的execl fileUrl 字段 内容
     */
    private String originHtml;

    /**
     * 原始记录PDFXML
     */
    private String originPdfXml;

    /**
     * 原始记录状态 OriginDataStatusEnum
     */
    private int status;

    /**
     * 原始记录状态名称
     */
    private String statusName;

    /**
     * 原始记录部门
     */
    private String createDep;

    /**
     * 原始记录页面设置
     */
    private String pageSettingXml;

    /**
     * 原始记录子机构
     */
    private String subOrg;

    /**
     * 原始记录文件扩展名
     */
    private String originFileExt;

    /**
     * 承接小组F6
     */
    private String teamName;

    /**
     * 复审人
     */
    private String reviewer;

    /**
     * 附件下载地址初始化存储是空模板，修改以后可以传入
     */
    private String fileUrl;

    /**
     * 是否允许回退 0 不允许
     */
    private Integer allowBack;

    /**
     * 检测方式F1  自检|外包
     */
    private String methodType;

    /**
     * 执行期限
     */
    private String contractEndDate;

    /**
     * 是否军方代表F9 是|否
     */
    private String armyFlg;

    /**
     * 规格F1
     */
    private String specification;

    /**
     * 检测后处理F12
     */
    private String finalProcess;

    /**
     * 管号
     */
    private String pipeNumber;

    /**
     * 样品批次
     */
    private String sampleBatch;

    /**
     * 退回原因
     */
    private String reasonBack;

    /**
     * 完成时间
     */
    private Date completeTime;

}

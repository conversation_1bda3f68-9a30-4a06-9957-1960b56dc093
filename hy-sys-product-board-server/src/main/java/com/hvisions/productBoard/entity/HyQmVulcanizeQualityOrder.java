package com.hvisions.productBoard.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 硫化过程检验单
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("sys_qms.hy_qm_vulcanize_quality_order")
public class HyQmVulcanizeQualityOrder extends SysBase {
    /**
    * 军种
    */
    private String armType;

    /**
    * 批次
    */
    private String batchNum;

    /**
    * 创建人
    */
    private String creatorName;

    /**
    * 交付单位
    */
    private String customCompany;

    /**
    * 机型
    */
    private String machineType;

    /**
    * 硫化单号
    */
    private String orderCode;

    /**
    * 产品编码
    */
    private String productCode;

    /**
    * 产品规格
    */
    private String productEigenvalue;

    /**
    * 产品型号
    */
    private String productModel;

    /**
    * 产品名称
    */
    private String productName;

    /**
    * 生产类型
    */
    private String productionTypeName;

    /**
    * 合格数量
    */
    private Integer qualityPassCount;

    /**
    * 检验日期
    */
    private Date qualityTime;

    /**
    * 检验人
    */
    private Integer qualityUserId;

    /**
    * 检验人
    */
    private String qualityUserName;

    /**
    * 备注
    */
    private String remark;

    /**
    * 销售单号
    */
    private String saleOrderCode;

    /**
    * 供应商
    */
    private String supplierName;

    /**
    * 总根数
    */
    private Integer totalCount;

    /**
    * 硫化胶料
    */
    private String vulcanizeMaterial;

    /**
    * 硫化状态
    */
    private Integer vulcanizeState;

    /**
    * 硫化次数
    */
    private Integer vulcanizeTimes;

    /**
    * 生产任务单号
    */
    private String workOrderCode;

    /**
    * 原始记录单
    */
    private String workSubOrderCode;

    /**
    * 拆分标记
    */
    private Boolean splitTag;

    /**
    * 生产任务单id
    */
    private Integer workOrderId;

    /**
    * 图纸状态
    */
    private Integer drawingState;

    /**
    * 最新图纸更新时间
    */
    private Date drawingUpdateDateTime;

    /**
    * 图纸版本
    */
    private String drawingVersion;

    /**
    * 最新图纸版本
    */
    private String drawingVersionLatest;

    /**
    * 批次
    */
    private String materialBatchNum;

    /**
    * 物料编码
    */
    private String materialCode;

    /**
    * 物料ID
    */
    private Integer materialId;

    /**
    * 物料名称
    */
    private String materialName;

    /**
    * 工艺路线编码
    */
    private String materialRouteCode;

    /**
    * 物料工艺路线ID
    */
    private Integer materialRouteId;

    /**
    * 工艺路线名称
    */
    private String materialRouteName;
}
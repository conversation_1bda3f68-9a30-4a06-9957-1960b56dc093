package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 五部总装原始记录单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_pms.hy_pm_work_sub_order")
public class PmAssemblyWorkSubOrder extends SysBase {

    /**
     * 父原始记录单号
     */
    private String parentCode;

    /**
     * 原始记录单号
     */
    private String code;

    /**
     * SAP生产订单号
     */
    private String sapCode;

    /**
     * 是否特殊软管
     */
    private Boolean isSpecial;

    /**
     * 是否试验任务单
     */
    private Boolean isTest;

    /**
     * 是否已经组装检验
     */
    private Boolean isAssemblyInspected;

    /*
        生产计划下发 选择军用(普通)/民用(民用) 产品数据库中有生产类型(插入式/分离式)
        试验任务单(正常生产)  根据产品数据库 + 是否民用 决定类型 为普通/民用 + 插入式/分离式
        试验任务单(非正常生产) 创建时人为选择(插入式/分离式) 决定类型 为试验 + 插入式/分离式
        普通/多段 由产品数据库 段数决定 1=普通 大于1=多段

        一段普通插入式
        一段普通分离式
        一段民用插入式
        一段民用分离式
        一段试验插入式
        一段试验分离式
        多段普通插入式
        多段普通分离式
        多段民用插入式
        多段民用分离式
        多段试验插入式(无)
        多段试验分离式(无)
     */

    /**
     * 类型
     */
    private Integer type;

    /**
     * 任务单号
     */
    private String orderCode;

    /**
     * 任务单类型
     */
    private Integer orderType;

    /**
     * 任务单号来源
     */
    private String orderCodeSource;

    /**
     * 生产序号
     */
    private Integer seq;

    /**
     * 是否销售订单
     */
    private Boolean isSaleOrder;

    /**
     * 是否科研任务单
     */
    private Boolean isResearchOrder;

    /**
     * 销售订单号
     */
    private String saleOrderCode;

    /**
     * 销售订单明细ID
     */
    private Integer saleOrderLineId;

    /**
     * 科研任务单号
     */
    private String researchOrderCode;

    /**
     * 科研任务订单明细ID
     */
    private Integer researchOrderLineId;

    /**
     * 对方合同号
     */
    private String saleOrderContractCode;

    /**
     * 生产类型
     */
    private Integer productionType;

    /**
     * 产品类型
     */
    private Integer productType;

    /**
     * 军种
     */
    private String armyType;

    /**
     * 机型
     */
    private String machineType;

    /**
     * 交付单位
     */
    private String customCompany;

    /**
     * 物料ID
     */
    private Integer materialId;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 规格
     */
    private String eigenvalue;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 图号
     */
    private String drawingNo;

    /**
     * 物料单位
     */
    private String materialUnitName;

    /**
     * 物料单位
     */
    private String materialUnitSymbol;

    /**
     * bomID
     */
    private Integer bomId;

    /**
     * bom编码
     */
    private String bomCode;

    /**
     * bom名称
     */
    private String bomName;

    /**
     * bom版本
     */
    private String bomVersion;

    /**
     * 物料工艺路线ID
     */
    private Integer materialRouteId;

    /**
     * 工艺路线编码
     */
    private String materialRouteCode;

    /**
     * 工艺路线名称
     */
    private String materialRouteName;

    /**
     * 工艺路线备注
     */
    private String materialRouteRemark;

    /**
     * 图纸版本
     */
    private String drawingVersion;

    /**
     * 最新图纸版本
     */
    private String drawingVersionLatest;

    /**
     * 最新工艺路线备注
     */
    private String materialRouteRemarkLatest;

    /**
     * 图纸状态
     */
    private Integer drawingState;

    /**
     * 是否需要更新
     */
    private Boolean isDrawingNeedUpdate;

    /**
     * 图纸更新时间
     */
    private LocalDateTime drawingUpdateDateTime;

    /**
     * 最近完成工序
     */
    private String latestRouteOperationName;

    /**
     * 工序执行人
     */
    private Integer latestRouteOperationOperatorId;

    /**
     * 工序执行人
     */
    private String latestRouteOperationOperatorName;

    /**
     * 下发时间
     */
    private LocalDateTime issuedTime;

    /**
     * 计划开始日期时间
     */
    private LocalDateTime planBeginDateTime;

    /**
     * 计划结束日期时间
     */
    private LocalDateTime planEndDateTime;

    /**
     * 实际开始日期时间
     */
    private LocalDateTime actualBeginDateTime;

    /**
     * 实际结束日期时间
     */
    private LocalDateTime actualEndDateTime;

    /**
     * 计划数量
     */
    private BigDecimal planQuantity;

    /**
     * 实际产出数量
     */
    private BigDecimal productQuantity;

    /**
     * 合格数量
     */
    private BigDecimal qualifiedQuantity;

    /**
     * 不合格数量
     */
    private BigDecimal unqualifiedQuantity;

    /**
     * 批次
     */
    private String materialBatchNum;

    /**
     * 实际批次
     */
    private String actualMaterialBatchNum;

    // 待完工确认
    /**
     * 状态
     */
    private Integer state;

    /**
     * 车间ID
     */
    private Integer areaId;

    /**
     * 产线Id
     */
    private Integer cellId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 图纸软管长度
     */
    private String drawingHoseLength;

    /**
     * 工作压力
     */
    private String workingPressure;

    /**
     * 耐压压力
     */
    private String withstandPressure;

    /**
     * 收缩套压力
     */
    private String shrink;

    /**
     * 是否需要二维码
     */
    private String qrcode;

    /**
     * 组件段数
     */
    private Integer multiNum;

    /**
     * 投入物料单号
     */
    private String inputMaterialCode;

    /**
     * 下一个拆分序号
     */
    private Integer splitNextSeq = 1;

    /**
     * 是否被拆分过
     */
    private Boolean isSplit;

    /**
     * 产出凭证
     */
    private String outputMblnr;

    /**
     * 产出响应信息
     */
    private String outputMsg;

    /**
     * 产出响应信息
     */
    protected Integer createUserId;

    /**
     * 是否紧急
     */
    private Boolean isUrgency;
}

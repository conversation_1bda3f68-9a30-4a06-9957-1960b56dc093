package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 包装任务单
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_pms.hy_pm_pkg_order")
public class HyPmPkgOrder extends SysBase {
    /**
     * 包装任务单号
     */
    private String code;

    /**
     * 成品入库单号
     */
    private String completeDeliverOrderCode;

    /**
     * 合同号
     */
    private String contractNum;

    /**
     * 订货单位
     */
    private String customer;

    private Boolean flag;

    /**
     * 包装日期 单据完成日期
     */
    private Date pkgDate;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 生产日期
     */
    private Date productTime;

    /**
     * 包装总数量
     */
    private Integer quantity;

    /**
     * 异常原因
     */
    private String reason;

    /**
     * 销售订单号
     */
    private String saleOrderCode;

    /**
     * 军检方
     */
    private String soldierCheck;

    /**
     * 单据状态
     */
    private Integer state;

    /**
     * 验收提交单号
     */
    private String submitOrderCode;

    /**
     * 提交状态
     */
    private Integer submitState;

    /**
     * 单位
     */
    private String unit;

    /**
     * 消耗提交状态
     */
    private Integer consumeSubmitState;

    /**
     * 提交日期
     */
    private Date submitDate;

    /**
     * 生产任务单号
     */
    private String sapCode;

    /**
     * 计划结束时间
     */
    private Date planEndTime;

    /**
     * 计划开始时间
     */
    private Date planStartTime;

    /**
     * 是否科研任务
     */
    private Integer isResearch;
}
package com.hvisions.productBoard.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 完工入库单明细
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("sys_wms.hy_wms_complete_deliver_order_line")
public class HyWmsCompleteDeliverOrderLine extends SysBase {
    private BigDecimal actualQuantity;

    /**
    * 军种
    */
    private String armType;

    /**
    * 批次
    */
    private String batchNo;

    /**
    * 完工入库单号
    */
    private String code;

    /**
    * 产品编码
    */
    private String materialCode;

    /**
    * 规格
    */
    private String materialEigenvalue;

    /**
    * 产品名称
    */
    private String materialName;

    /**
    * 产品简称
    */
    private String materialNameDesc;

    /**
    * 包装任务单号
    */
    private String pkgOrderCode;

    /**
    * 包装任务单明细ID
    */
    private Integer pkgOrderLineId;

    /**
    * 型号
    */
    private String productModel;

    private BigDecimal quantity;

    /**
    * 销售批次
    */
    private String saleBatchNo;

    /**
    * 销售单号
    */
    private String saleOrderCode;

    /**
    * 单位
    */
    private String unit;

    /**
    * 原始记录单号
    */
    private String workSubOrderCode;

    /**
    * SAP生产订单号
    */
    private String sapWorkOrderCode;

    /**
    * 入库日期
    */
    private Date finishDate;

    /**
    * 状态
    */
    private Integer state;

    /**
    * 部门
    */
    private String department;

    /**
    * 物料转换任务单号
    */
    private String materialConverCode;

    /**
    * 业务单号类型
    */
    private Integer bizOrderType;

    /**
    * 业务单号类型
    */
    private String bizOrderTypeName;
}
package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_pms.hy_pm_work_sub_order_operation_output")
public class PmAssemblyWorkSubOrderOperationOutput extends SysBase {

    /**
     * 任务单号
     */
    private String orderCode;

    /**
     * 原始记录单
     */
    private Integer workSubOrderId;

    /**
     * 原始记录单编码
     */
    private String workSubOrderCode;

    /**
     * 工序名称
     */
    private String operationName;

    /**
     * 操作者
     */
    private Integer operatorId;

    /**
     * 操作者
     */
    private String operatorName;

    /**
     * 操作者
     */
    private String operatorCode;

    /**
     * 产出数量
     */
    private BigDecimal count;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品规格
     */
    private String productEigenvalue;

    /**
     * 产品批次
     */
    private String productBatchNum;

    /**
     * 销售订单号
     */
    private String saleOrderCode;

}

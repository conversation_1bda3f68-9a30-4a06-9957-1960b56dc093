package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 库存表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_wms.hy_wms_material_stocks")
public class WmsMaterialStocks extends SysBase {

    /**
     * 物料类型
     */
    private String materialTypeCode;
    /**
     * 物料类型名称
     */
    private String materialTypeName;
    /**
     * 物料分组
     */
    private String materialGroupCode;
    /**
     * 物料分组名称
     */
    private String materialGroupName;
    /**
     * 物料ID
     */
    private Integer materialId;
    /**
     * 物料名称
     */
    private String materialName;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 物料图号
     */
    private String drawingNum;
    /**
     * 物料规格
     */
    private String materialEigenvalue;
    /**
     * 物料简称
     */
    private String materialDescribe;
    /**
     * 物料单位
     */
    private String materialUnit;
    /**
     * 物料单位中文
     */
    private String materialUnitName;
    /**
     * 请购在途数量
     */
    private BigDecimal purchaseApplyQuantity;
    /**
     * 请购在途共享数量
     */
    private BigDecimal purchaseApplyPublicQuantity;
    /**
     * 采购在途数量
     */
    private BigDecimal purchaseQuantity;
    /**
     * 在库数量
     */
    private BigDecimal quantity;
    /**
     * 可用数量
     */
    private BigDecimal useableQuantity;
    /**
     * 发货在途数量
     */
    private BigDecimal shippingQuantity;
    /**
     * 领料在途
     */
    private BigDecimal deliverQuantity;
    /**
     * 领料单锁定量
     */
    private BigDecimal deliverLockQuantity;
    /**
     * 在检数量
     */
    private BigDecimal qualityQuantity;
    /**
     * 冻结数量
     */
    private BigDecimal frozenQuantity;
    /**
     * 仓库类型编码
     */
    private String wareLocationClassCode;
    /**
     * 仓库类型名称
     */
    private String wareLocationClassName;
    /**
     * 仓库编码：仓库或车间一级
     */
    private String wareLocationCode;
    /**
     * 仓库名称
     */
    private String wareLocationName;
}
package com.hvisions.productBoard.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * BOM 项
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_hiper_base.hv_bm_bom_item")
public class BmBomItem extends SysBase {

    /**
     * bom 主键
     */
    private Integer bomId;
    /**
     * bomItem编码（非空）
     */
    private String bomItemCode;
    /**
     * bomItem类型
     */
    private Integer bomItemType;
    /**
     * bomItem数量
     */
    private BigDecimal bomItemCount;

    /**
     * bom明细单位名称
     */
    private String bomItemUnitName;

    /**
     * bom明细单位
     */
    private String bomItemUnitCode;
    /**
     * bomItem名称（非空）
     */
    private String bomItemName;
    /**
     * materials主键
     */
    private Integer materialsId;

}

package com.hvisions.productBoard.util;

import com.hvisions.productBoard.req.CommonPageReq;

/**
 * PageHelper 分页适配器工具类
 */
public class PageHelperAdapter {
    
    /**
     * 创建分页请求对象
     * @param current 当前页码，默认为1
     * @param size 每页大小，默认为10
     * @return CommonPageReq
     */
    public static CommonPageReq createPageReq(Integer current, Integer size) {
        return CommonPageReq.of(
            current != null ? current : 1,
            size != null ? size : 10
        );
    }
}

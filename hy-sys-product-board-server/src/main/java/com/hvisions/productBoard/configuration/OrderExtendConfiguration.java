package com.hvisions.productBoard.configuration;

import com.hvisions.common.utils.SqlFactoryUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: OrderExtendConfiguration</p >
 * <p>Description: 工单扩展</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/1/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Configuration
public class OrderExtendConfiguration {

    @Bean
    SqlFactoryUtil getSqlFactory() {
        return new SqlFactoryUtil();
    }

//    /**
//     * 工单扩展服务
//     * @return 工单扩展服务
//     */
//    @Bean(value = "hv_pm_work_order_extend")
//    BaseExtendService getWorkOrderExtendService(SqlFactoryUtil getSqlFactory) {
//        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
//        extendInfoParam.setOriginTableName(HyPmAssemblyWorkOrderConst.WORK_ORDER_TABLE_NAME);
//        extendInfoParam.setOriginTableIdName(HyPmAssemblyWorkOrderConst.WORK_ORDER_ID);
//        extendInfoParam.setExtendTableName(HyPmAssemblyWorkOrderConst.WORK_ORDER_EXTEND);
//        return getSqlFactory.getSqlBridge(extendInfoParam);
//    }
//
//    /**
//     * 工单类型扩展服务
//     * @return 工单类型扩展服务
//     */
//    @Bean(value = "hv_pm_work_order_type_extend")
//    BaseExtendService getWorkOrderTypeExtendService(SqlFactoryUtil getSqlFactory) {
//        ExtendInfoParam extendInfoParam = new ExtendInfoParam();
//        extendInfoParam.setOriginTableName(HyPmAssemblyWorkOrderConst.WORK_ORDER_TYPE_TABLE_NAME);
//        extendInfoParam.setOriginTableIdName(HyPmAssemblyWorkOrderConst.WORK_ORDER_TYPE_ID);
//        extendInfoParam.setExtendTableName(HyPmAssemblyWorkOrderConst.WORK_ORDER_TYPE_EXTEND);
//        return getSqlFactory.getSqlBridge(extendInfoParam);
//    }
}

package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.enums.PackageIndexTypeEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.resp.PmPackageCompleteOutputResp;
import com.hvisions.productBoard.resp.PmPackageDataOverviewResp;
import com.hvisions.productBoard.service.PmPackageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "办公室看板 - 包装")
@RestController
@RequestMapping("/pms/package")
@RequiredArgsConstructor
public class PmPackageController {

    @Resource
    private PmPackageService pmPackageService;

    @GetMapping("/dataOverview")
    @ApiOperation(value = "订单概况")
    public PmPackageDataOverviewResp getDataOverview() {
        return pmPackageService.getDataOverview();
    }

    @GetMapping("/qualityYear")
    @ApiOperation(value = "包装完成统计（年）")
    public List<PmPackageCompleteOutputResp> getQualityYear() {
        return pmPackageService.getOutput(TimePeriodEnum.YEAR, PackageIndexTypeEnum.PACKAGE);
    }

    @GetMapping("/qualityMonth")
    @ApiOperation(value = "包装完成统计（月）")
    public List<PmPackageCompleteOutputResp> getQualityMonth() {
        return pmPackageService.getOutput(TimePeriodEnum.MONTH, PackageIndexTypeEnum.PACKAGE);
    }

    @GetMapping("/qualityWeek")
    @ApiOperation(value = "包装完成统计（周）")
    public List<PmPackageCompleteOutputResp> getQualityWeek() {
        return pmPackageService.getOutput(TimePeriodEnum.WEEK, PackageIndexTypeEnum.PACKAGE);
    }

    @GetMapping("/militaryYear")
    @ApiOperation(value = "入库完成统计（年）")
    public List<PmPackageCompleteOutputResp> getMilitaryYear() {
        return pmPackageService.getOutput(TimePeriodEnum.YEAR, PackageIndexTypeEnum.STORAGE);
    }

    @GetMapping("/militaryMonth")
    @ApiOperation(value = "入库完成统计（月）")
    public List<PmPackageCompleteOutputResp> getMilitaryMonth() {
        return pmPackageService.getOutput(TimePeriodEnum.MONTH, PackageIndexTypeEnum.STORAGE);
    }

    @GetMapping("/militaryWeek")
    @ApiOperation(value = "入库完成统计（周）")
    public List<PmPackageCompleteOutputResp> getMilitaryWeek() {
        return pmPackageService.getOutput(TimePeriodEnum.WEEK, PackageIndexTypeEnum.STORAGE);
    }
}
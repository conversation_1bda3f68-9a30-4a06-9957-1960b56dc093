package com.hvisions.productBoard.controller;

import com.hvisions.common.utils.DtoMapper;
import com.hvisions.productBoard.entity.WmsMaterialStocks;
import com.hvisions.productBoard.materialStocks.dto.MaterialStockQueryDTO;
import com.hvisions.productBoard.materialStocks.vo.WmsMaterialStocksVO;
import com.hvisions.productBoard.service.WmsMaterialStocksService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据分析
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/wms-material-stocks")
public class WmsMaterialStocksController {

    @Resource
    private WmsMaterialStocksService wmsMaterialStocksService;


    /**
     * 支持按照物料编码集合、物料类型集合、部门集合查询库存数据
     * 最终返回数据格式为
     {
     "materialCode": "80385571",
     "materialName": "xxx",
     "quantity": 100,
     "warehouse": "SY05",
     "batchStocks": [
         {
         "materialCode": "80385571",
         "materialName": "xxx",
         "quantity": 20,
         "materialBatchNum": "2024001"
         "warehouse": "SY05",
         },
         {
         "materialCode": "80385571",
         "materialName": "xxx",
         "quantity": 80,
         "materialBatchNum": "2024002"
         "warehouse": "SY05",
         }
     ]
     }
     注意数据库中库存表存储库位为CJSY05-ZZ01  但是查询中库位只到CJSY05-ZZ级别
     */
    @PostMapping("/query")
    public List<WmsMaterialStocksVO> query(@RequestBody MaterialStockQueryDTO query) {
        return wmsMaterialStocksService.query(query);
    }
}

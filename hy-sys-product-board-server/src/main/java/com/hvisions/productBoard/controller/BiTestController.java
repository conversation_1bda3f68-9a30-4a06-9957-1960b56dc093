package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.dto.LmContractDTO;
import com.hvisions.productBoard.req.BiTestFinishedQuantityStatisticReq;
import com.hvisions.productBoard.resp.BiTestFinishedQuantityStatisticResp;
import com.hvisions.productBoard.resp.BiTestFinishedQuantityTodayAndCurrMonthResp;
import com.hvisions.productBoard.service.BiTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "生产综合看板-五部试验中心")
@RequiredArgsConstructor
@RequestMapping("/bi/test")
@RestController
public class BiTestController {

    private final BiTestService biTestService;

    @ApiOperation(value = "委托订单检测状态实时监控")
    @PostMapping(value = "/key-order")
    public List<LmContractDTO> getKeyOrderList() {
        return biTestService.getKeyOrderList();
    }

    @ApiOperation(value = "试验任务完成情况")
    @PostMapping(value = "/finished-quantity/today-and-curr-month/test")
    public BiTestFinishedQuantityTodayAndCurrMonthResp getFinishedQuantityTodayAndCurrMonth1() {
        return biTestService.getFinishedQuantityTodayAndCurrMonth1();
    }

    @ApiOperation(value = "耐压任务完成情况")
    @PostMapping(value = "/finished-quantity/today-and-curr-month/pressure")
    public BiTestFinishedQuantityTodayAndCurrMonthResp getFinishedQuantityTodayAndCurrMonth2() {
        return biTestService.getFinishedQuantityTodayAndCurrMonth2();
    }

    @ApiOperation(value = "软管组件耐压完成情况")
    @PostMapping(value = "/finished-quantity-statistic")
    public BiTestFinishedQuantityStatisticResp getFinishedQuantityStatistic(@RequestBody BiTestFinishedQuantityStatisticReq req) {
        return biTestService.getFinishedQuantityStatistic2(req);
    }

}

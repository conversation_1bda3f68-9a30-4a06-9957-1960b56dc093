package com.hvisions.productBoard.controller;

import org.springframework.data.domain.Page;
import com.hvisions.productBoard.resp.*;
import com.hvisions.productBoard.service.BiInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "综合看板 - 质检")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bi/inspection")
public class BiInspectionController {

    @Resource
    private BiInspectionService biInspectionService;

    @ApiOperation(value = "待检任务数")
    @GetMapping("/dataOverview")
    public BiInspectionDataOverviewResp getDataOverview() {
        return biInspectionService.getDataOverview();
    }

    @ApiOperation(value = "近三月不合格品分布")
    @GetMapping("/unqualifiedDistribution")
    public List<BiInspectionUnqualifiedDistributionResp> getUnqualifiedDistribution() {
        return biInspectionService.getUnqualifiedDistribution();
    }

    @ApiOperation(value = "关键订单检验进度实时监控")
    @GetMapping("/keyOrder")
    public List<BiInspectionKeyOrderResp> getKeyOrder() {
        return biInspectionService.getKeyOrder();
    }

    @ApiOperation(value = "待检硫化订单")
    @GetMapping("/vulcanizeOrder")
    public Page<BiInspectionVulcanizeOrderResp> getVulcanizeOrder(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        return biInspectionService.getVulcanizeOrder(current, size);
    }

    @ApiOperation(value = "近七日检验完成情况")
    @GetMapping("/completeOutput")
    public List<BiInspectionCompleteOutputResp> getCompleteOutput() {
        return biInspectionService.getCompleteOutput();
    }

    @ApiOperation(value = "通知列表")
    @GetMapping("/notice")
    public String getNotice() {
        return biInspectionService.getNotice();
    }
}

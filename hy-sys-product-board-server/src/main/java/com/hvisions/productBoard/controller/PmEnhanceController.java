package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.resp.PmEnhanceCompleteRateResp;
import com.hvisions.productBoard.resp.PmEnhanceDataOverviewResp;
import com.hvisions.productBoard.service.PmEnhanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "办公室看板 - 增强")
@RestController
@RequiredArgsConstructor
@RequestMapping("/pms/enhance")
public class PmEnhanceController {

    @Resource
    private PmEnhanceService pmEnhanceService;

    @GetMapping("/dataOverview")
    @ApiOperation(value = "订单概况")
    public PmEnhanceDataOverviewResp getDataOverview() {
        return pmEnhanceService.getDataOverview();
    }

    @GetMapping("/rateYear")
    @ApiOperation(value = "计划达成率（年）")
    public List<PmEnhanceCompleteRateResp> getRateYear() {
        return pmEnhanceService.getCompleteRate(TimePeriodEnum.YEAR);
    }

    @GetMapping("/rateMonth")
    @ApiOperation(value = "计划达成率（月）")
    public List<PmEnhanceCompleteRateResp> getRateMonth() {
        return pmEnhanceService.getCompleteRate(TimePeriodEnum.MONTH);
    }

    @GetMapping("/rateWeek")
    @ApiOperation(value = "计划达成率（周）")
    public List<PmEnhanceCompleteRateResp> getRateWeek() {
        return pmEnhanceService.getCompleteRate(TimePeriodEnum.WEEK);
    }
}

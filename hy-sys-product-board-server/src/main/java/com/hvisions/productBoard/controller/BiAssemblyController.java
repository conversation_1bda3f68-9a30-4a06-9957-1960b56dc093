package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.req.BiAssemblyKeyOrderReq;
import com.hvisions.productBoard.resp.BiAssemblyKeyOrderResp;
import com.hvisions.productBoard.resp.BiAssemblyPlanStatisticResp;
import com.hvisions.productBoard.resp.BiAssemblyStepFinishedQuantityLastAndCurrWeekResp;
import com.hvisions.productBoard.service.BiAssemblyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "生产综合看板-五部总装看板")
@RequiredArgsConstructor
@RequestMapping("/bi/assembly")
@RestController
public class BiAssemblyController {

    private final BiAssemblyService biAssemblyService;

    @ApiOperation(value = "工位任务生产情况")
    @PostMapping("/step-finished-quantity/last-and-curr-week")
    public List<BiAssemblyStepFinishedQuantityLastAndCurrWeekResp> getStepFinishedQuantityOfLastAndCurrWeek() {
        return biAssemblyService.getStepFinishedQuantityOfLastAndCurrWeek();
    }

    @ApiOperation(value = "关键订单生产状态实时监控")
    @PostMapping("/key-order")
    public Page<BiAssemblyKeyOrderResp> getKeyOrders(@RequestBody BiAssemblyKeyOrderReq req) {
        return biAssemblyService.getKeyOrders(req);
    }

    @ApiOperation(value = "本月需完成计划")
    @PostMapping("/plan-statistic/curr-month")
    public BiAssemblyPlanStatisticResp getPlanStatisticOfCurrMonth() {
        return biAssemblyService.getPlanStatisticOfCurrMonth();
    }
}

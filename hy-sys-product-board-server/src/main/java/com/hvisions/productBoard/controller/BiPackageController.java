package com.hvisions.productBoard.controller;

import org.springframework.data.domain.Page;
import com.hvisions.productBoard.resp.*;
import com.hvisions.productBoard.service.BiPackageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "综合看板 - 包装")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bi/package")
public class BiPackageController {

    @Resource
    private BiPackageService biPackageService;

    @ApiOperation(value = "待包装任务统计")
    @GetMapping("/dataOverview")
    public BiPackageDataOverviewResp getDataOverview() {
        return biPackageService.getDataOverview();
    }

    @ApiOperation(value = "当班人员")
    @GetMapping("/onDuty")
    public List<BiPackageOnDutyResp> getOnDuty() {
        return biPackageService.getOnDuty();
    }

    @ApiOperation(value = "待包装任务清单")
    @GetMapping("/packageOrder")
    public Page<BiPackageOrderResp> getPackageOrder(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size
    ) {
        return biPackageService.getPackageOrder(current, size);
    }

    @ApiOperation(value = "待入库任务清单")
    @GetMapping("/warehouseOrder")
    public Page<BiWarehouseOrderResp> getWarehouseOrder(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {
        return biPackageService.getWarehouseOrder(current, size);
    }

    @ApiOperation(value = "包装任务完成情况")
    @GetMapping("/completeOutput")
    public List<BiPackageCompleteOutputResp> getCompleteOutput() {
        return biPackageService.getCompleteOutput();
    }

    @ApiOperation(value = "通知列表")
    @GetMapping("/notice")
    public String getNotice() {
        return biPackageService.getNotice();
    }
}

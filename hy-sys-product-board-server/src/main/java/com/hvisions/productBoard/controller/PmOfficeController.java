package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.req.PmOfficeAssemblyPlanQuantityAndPercentReq;
import com.hvisions.productBoard.resp.PmOfficeAssemblyPlanQuantityAndPercentResp;
import com.hvisions.productBoard.resp.PmOfficeOverviewResp;
import com.hvisions.productBoard.service.PmOfficeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "生产首页-五部办公室首页")
@RequiredArgsConstructor
@RequestMapping("/pms/office")
@RestController
public class PmOfficeController {

    private final PmOfficeService pmOfficeService;

    @ApiOperation(value = "数据概览")
    @PostMapping("/overview")
    public PmOfficeOverviewResp getOverview() {
        return pmOfficeService.getOverview();
    }

    @ApiOperation(value = "总装计划达成率")
    @PostMapping(value = "/assembly/plan-quantity-and-percent")
    public PmOfficeAssemblyPlanQuantityAndPercentResp getAssemblyPlanQuantityAndPercent(@RequestBody PmOfficeAssemblyPlanQuantityAndPercentReq req) {
        return pmOfficeService.getAssemblyPlanQuantityAndPercent(req);
    }

}

package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.req.BiAssemblyStepFinishedQuantityStatisticReq;
import com.hvisions.productBoard.resp.BiAssemblyStepFinishedQuantityStatisticResp;
import com.hvisions.productBoard.resp.BiAssemblyStepKeyOrderResp;
import com.hvisions.productBoard.service.BiAssemblyStepService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "生产综合看板-五部总装工位看板")
@RequiredArgsConstructor
@RequestMapping("/bi/assembly-step")
@RestController
public class BiAssemblyStepController {

    private final BiAssemblyStepService biAssemblyStepService;

    @ApiOperation(value = "获取关键任务清单")
    @PostMapping(value = "/key-order")
    public List<BiAssemblyStepKeyOrderResp> getKeyOrderList() {
        return biAssemblyStepService.getKeyOrderList();
    }

    @ApiOperation(value = "获取近7天完成数量统计")
    @PostMapping(value = "/finished-quantity/last7days")
    public BiAssemblyStepFinishedQuantityStatisticResp getFinishQuantityStatisticOfLast7Days(@RequestBody BiAssemblyStepFinishedQuantityStatisticReq req) {
        return biAssemblyStepService.getFinishQuantityStatisticOfLast7Days(req);
    }

}

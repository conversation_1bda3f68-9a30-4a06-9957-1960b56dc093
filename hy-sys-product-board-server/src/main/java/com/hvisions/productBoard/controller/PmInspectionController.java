package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.enums.InspectionTypeEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.resp.PmInspectionCompleteOutputResp;
import com.hvisions.productBoard.resp.PmInspectionDataOverviewResp;
import com.hvisions.productBoard.service.PmInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "办公室看板 - 质检")
@RestController
@RequiredArgsConstructor
@RequestMapping("/pms/inspection")
public class PmInspectionController {

    @Resource
    private PmInspectionService pmInspectionService;

    @GetMapping("/dataOverview")
    @ApiOperation(value = "订单概况")
    public PmInspectionDataOverviewResp getDataOverview() {
        return pmInspectionService.getDataOverview();
    }

    @GetMapping("/qualityYear")
    @ApiOperation(value = "质检完成统计（年）")
    public List<PmInspectionCompleteOutputResp> getQualityYear() {
        return pmInspectionService.getOutput(TimePeriodEnum.YEAR, InspectionTypeEnum.QUALITY);
    }

    @GetMapping("/qualityMonth")
    @ApiOperation(value = "质检完成统计（月）")
    public List<PmInspectionCompleteOutputResp> getQualityMonth() {
        return pmInspectionService.getOutput(TimePeriodEnum.MONTH, InspectionTypeEnum.QUALITY);
    }

    @GetMapping("/qualityWeek")
    @ApiOperation(value = "质检完成统计（周）")
    public List<PmInspectionCompleteOutputResp> getQualityWeek() {
        return pmInspectionService.getOutput(TimePeriodEnum.WEEK, InspectionTypeEnum.QUALITY);
    }

    @GetMapping("/militaryYear")
    @ApiOperation(value = "军检完成统计（年）")
    public List<PmInspectionCompleteOutputResp> getMilitaryYear() {
        return pmInspectionService.getOutput(TimePeriodEnum.YEAR, InspectionTypeEnum.MILITARY);
    }

    @GetMapping("/militaryMonth")
    @ApiOperation(value = "军检完成统计（月）")
    public List<PmInspectionCompleteOutputResp> getMilitaryMonth() {
        return pmInspectionService.getOutput(TimePeriodEnum.MONTH, InspectionTypeEnum.MILITARY);
    }

    @GetMapping("/militaryWeek")
    @ApiOperation(value = "军检完成统计（周）")
    public List<PmInspectionCompleteOutputResp> getMilitaryWeek() {
        return pmInspectionService.getOutput(TimePeriodEnum.WEEK, InspectionTypeEnum.MILITARY);
    }
}

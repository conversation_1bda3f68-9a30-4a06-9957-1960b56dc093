package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.req.BiTestFinishedQuantityStatisticReq;
import com.hvisions.productBoard.req.PmAssemblyStepOutputReq;
import com.hvisions.productBoard.req.PmOfficeAssemblyPlanQuantityAndPercentReq;
import com.hvisions.productBoard.resp.*;
import com.hvisions.productBoard.service.BiOfficeService;
import com.hvisions.productBoard.service.BiTestService;
import com.hvisions.productBoard.service.PmAssemblyService;
import com.hvisions.productBoard.service.PmOfficeService;
import com.hvisions.productBoard.vo.SaSaleOrderLineVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "生产综合看板-五部办公室看板")
@RequiredArgsConstructor
@RequestMapping("/bi/office")
@RestController
public class BiOfficeController {

    private final BiOfficeService biOfficeService;
    private final BiTestService biTestService;
    private final PmOfficeService pmOfficeService;
    private final PmAssemblyService pmAssemblyService;

    // ! 整体 -----------------------------------------------------------------------------------------------------------

    @ApiOperation(value = "合同概览")
    @PostMapping("/overview")
    public BiOfficeOverviewResp getOverview() {
        return biOfficeService.getOverview();
    }

    @ApiOperation(value = "临期订单")
    @PostMapping("/near-order")
    public List<SaSaleOrderLineVO> getNearOrderList() {
        return biOfficeService.getNearOrderList();
    }

    @ApiOperation(value = "异常订单")
    @PostMapping("/error-order")
    public List<SaSaleOrderLineVO> getErrorOrderList() {
        return biOfficeService.getErrorOrderList();
    }

    // ! 总装 -----------------------------------------------------------------------------------------------------------

    @ApiOperation(value = "总装-订单概览")
    @PostMapping(value = "/assembly/overview")
    public BiOfficeAssemblyOverviewResp getAssemblyOverview() {
        return biOfficeService.getAssemblyOverview();
    }

    @ApiOperation(value = "总装-工位产出")
    @PostMapping(value = "/assembly/step-output")
    public PmAssemblyStepOutputResp getAssemblyStepOutput(@RequestBody PmAssemblyStepOutputReq req) {
        return pmAssemblyService.getStepOutput(req);
    }

    @ApiOperation(value = "总装-计划完成率")
    @PostMapping(value = "/assembly/plan-quantity-and-percent")
    public PmOfficeAssemblyPlanQuantityAndPercentResp getAssemblyPlanQuantityAndPercent(@RequestBody PmOfficeAssemblyPlanQuantityAndPercentReq req) {
        return pmOfficeService.getAssemblyPlanQuantityAndPercent(req);
    }

    // ! 试验 -----------------------------------------------------------------------------------------------------------

    @ApiOperation(value = "试验-订单概览")
    @PostMapping(value = "/test/overview")
    public BiOfficeTestOverviewResp getTestOverview() {
        return biOfficeService.getTestOverview();
    }

    @ApiOperation(value = "试验-试验完成统计")
    @PostMapping(value = "/test/finished-quantity-statistic/test")
    public BiTestFinishedQuantityStatisticResp getFinishedQuantityStatistic1(@RequestBody BiTestFinishedQuantityStatisticReq req) {
        return biTestService.getFinishedQuantityStatistic1(req);
    }

    @ApiOperation(value = "试验-耐压完成统计")
    @PostMapping(value = "/test/finished-quantity-statistic/pressure")
    public BiTestFinishedQuantityStatisticResp getFinishedQuantityStatistic2(@RequestBody BiTestFinishedQuantityStatisticReq req) {
        return biTestService.getFinishedQuantityStatistic2(req);
    }
}

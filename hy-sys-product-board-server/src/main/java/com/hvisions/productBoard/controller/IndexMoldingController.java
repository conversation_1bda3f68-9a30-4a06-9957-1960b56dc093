package com.hvisions.productBoard.controller;

import org.springframework.data.domain.Page;
import com.hvisions.productBoard.resp.IndexMoldingDataOverviewResp;
import com.hvisions.productBoard.resp.IndexMoldingKeyOrderResp;
import com.hvisions.productBoard.resp.IndexMoldingTestOrderResp;
import com.hvisions.productBoard.service.IndexMoldingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "生产首页 - 成型")
@RestController
@RequiredArgsConstructor
@RequestMapping("/index/molding")
public class IndexMoldingController {

    @Resource
    private IndexMoldingService indexMoldingService;

    @ApiOperation(value = "数据概览")
    @GetMapping("/dataOverview")
    public IndexMoldingDataOverviewResp getDataOverview() {
        return indexMoldingService.getDataOverview();
    }

    @ApiOperation(value = "生产订单")
    @GetMapping("/keyOrder")
    public Page<IndexMoldingKeyOrderResp> getKeyOrder(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size
    ) {
        return indexMoldingService.getKeyOrder(current, size);
    }

    @ApiOperation(value = "试制订单")
    @GetMapping("/testOrder")
    public Page<IndexMoldingTestOrderResp> getTestOrder(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size
    ) {
        return indexMoldingService.getTestOrder(current, size);
    }

    @ApiOperation(value = "关键原料库存")
    @GetMapping("/keyMaterials")
    public String getKeyMaterials() {
        return indexMoldingService.getKeyMaterials();
    }

    @ApiOperation(value = "公告")
    @GetMapping("/notice")
    public String getNotice() {
        return indexMoldingService.getNotice();
    }
}

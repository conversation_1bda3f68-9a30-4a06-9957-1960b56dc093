package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.req.PmAssemblyKeyOrderReq;
import com.hvisions.productBoard.req.PmAssemblyStepOutputReq;
import com.hvisions.productBoard.resp.PmAssemblyKeyOrderResp;
import com.hvisions.productBoard.resp.PmAssemblyOverviewResp;
import com.hvisions.productBoard.resp.PmAssemblyStepOutputResp;
import com.hvisions.productBoard.service.PmAssemblyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "生产首页-五部总装首页")
@RequiredArgsConstructor
@RequestMapping("/pms/assembly")
@RestController
public class PmAssemblyController {

    private final PmAssemblyService pmAssemblyService;

    @ApiOperation(value = "数据总览")
    @PostMapping("/overview")
    public PmAssemblyOverviewResp getOverview() {
        return pmAssemblyService.getOverview();
    }

    @ApiOperation(value = "工位产出记录")
    @PostMapping("/step-output")
    public PmAssemblyStepOutputResp getStepOutput(@RequestBody PmAssemblyStepOutputReq req) {
        return pmAssemblyService.getStepOutput(req);
    }

    @ApiOperation(value = "关键订单生产状态实时监控")
    @PostMapping("/key-order")
    public Page<PmAssemblyKeyOrderResp> getKeyOrders(@RequestBody PmAssemblyKeyOrderReq req) {
        return pmAssemblyService.getKeyOrders(req);
    }

    // ? 硫化后返厂待检

    // ? 公告

}

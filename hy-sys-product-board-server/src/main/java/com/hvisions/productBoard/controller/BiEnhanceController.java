package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.resp.BiEnhanceDataOverviewResp;
import com.hvisions.productBoard.service.BiEnhanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "综合看板 - 增强")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bi/enhance")
public class BiEnhanceController {

    @Resource
    private BiEnhanceService biEnhanceService;

    @ApiOperation(value = "安全生产累计天数")
    @GetMapping("safeDays")
    public String getSafeDays() {
        return biEnhanceService.getSafeDays();
    }

    @ApiOperation(value = "本月需完成计划")
    @GetMapping("/dataOverview")
    public BiEnhanceDataOverviewResp getDataOverview() {
        return biEnhanceService.getDataOverview();
    }

    @ApiOperation(value = "关键原料库存情况")
    @GetMapping("/keyMaterials")
    public String getKeyMaterials() {
        return biEnhanceService.getKeyMaterials();
    }

    @ApiOperation(value = "订单生产状态实时监控")
    @GetMapping("/keyOrder")
    public String getKeyOrder() {
        return biEnhanceService.getKeyOrder();
    }

    @ApiOperation(value = "关键工位任务完成情况")
    @GetMapping("/keyOrderCompleteOutput")
    public String getKeyWorkOrderCompleteOutput() {
        return biEnhanceService.getKeyWorkOrderCompleteOutput();
    }

    @ApiOperation(value = "通知列表")
    @GetMapping("/notice")
    public String getNotice() {
        return biEnhanceService.getNotice();
    }
}

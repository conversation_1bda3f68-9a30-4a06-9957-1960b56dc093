package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.resp.PmMoldingCompleteRateResp;
import com.hvisions.productBoard.resp.PmMoldingDataOverviewResp;
import com.hvisions.productBoard.resp.PmMoldingStepOutputResp;
import com.hvisions.productBoard.service.PmMoldingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "办公室看板 - 成型")
@RestController
@RequiredArgsConstructor
@RequestMapping("/pms/molding")
public class PmMoldingController {

    @Resource
    private PmMoldingService pmMoldingService;

    @GetMapping("/dataOverview")
    @ApiOperation(value = "订单概况")
    public PmMoldingDataOverviewResp getDataOverview() {
        return pmMoldingService.getDataOverview();
    }

    @GetMapping("/rateYear")
    @ApiOperation(value = "计划达成率（年）")
    public List<PmMoldingCompleteRateResp> getRateYear() {
        return pmMoldingService.getCompleteRate(TimePeriodEnum.YEAR);
    }

    @GetMapping("/rateMonth")
    @ApiOperation(value = "计划达成率（月）")
    public List<PmMoldingCompleteRateResp> getRateMonth() {
        return pmMoldingService.getCompleteRate(TimePeriodEnum.MONTH);
    }

    @GetMapping("/rateWeek")
    @ApiOperation(value = "计划达成率（周）")
    public List<PmMoldingCompleteRateResp> getRateWeek() {
        return pmMoldingService.getCompleteRate(TimePeriodEnum.WEEK);
    }

    @GetMapping("/stepOutputYear")
    @ApiOperation(value = "工位产出（年）")
    public List<PmMoldingStepOutputResp> getStepOutputYear() {
        return pmMoldingService.getStepOutput(TimePeriodEnum.YEAR);
    }

    @GetMapping("/stepOutputMonth")
    @ApiOperation(value = "工位产出（月）")
    public List<PmMoldingStepOutputResp> getStepOutputMonth() {
        return pmMoldingService.getStepOutput(TimePeriodEnum.MONTH);
    }

    @GetMapping("/stepOutputWeek")
    @ApiOperation(value = "工位产出（周）")
    public List<PmMoldingStepOutputResp> getStepOutputWeek() {
        return pmMoldingService.getStepOutput(TimePeriodEnum.WEEK);
    }
}

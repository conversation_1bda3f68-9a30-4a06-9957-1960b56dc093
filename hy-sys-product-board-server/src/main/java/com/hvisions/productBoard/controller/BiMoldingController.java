package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.resp.BiMoldingDataOverviewResp;
import com.hvisions.productBoard.resp.BiMoldingKeyOrderResp;
import com.hvisions.productBoard.resp.BiMoldingTestTaskResp;
import com.hvisions.productBoard.service.BiMoldingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "综合看板 - 成型")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bi/molding")
public class BiMoldingController {

    @Resource
    private BiMoldingService biMoldingService;

    @ApiOperation(value = "安全生产累计天数")
    @GetMapping("safeDays")
    public String getSafeDays() {
        return biMoldingService.getSafeDays();
    }

    @ApiOperation(value = "本月需完成计划")
    @GetMapping("/dataOverview")
    public BiMoldingDataOverviewResp getDataOverview() {
        return biMoldingService.getDataOverview();
    }

    @ApiOperation(value = "关键原料库存情况")
    @GetMapping("/keyMaterials")
    public String getKeyMaterials() {
        return biMoldingService.getKeyMaterials();
    }

    @ApiOperation(value = "订单生产状态实时监控")
    @GetMapping("/keyOrder")
    public List<BiMoldingKeyOrderResp> getKeyOrder() {
        return biMoldingService.getKeyOrder();
    }

    @ApiOperation(value = "试制订单列表")
    @GetMapping("/testWorkOrder")
    public List<BiMoldingTestTaskResp> getTestWorkOrder() {
        return biMoldingService.getTestWorkOrder();
    }

    @ApiOperation(value = "通知列表")
    @GetMapping("/notice")
    public String getNotice() {
        return biMoldingService.getNotice();
    }
}

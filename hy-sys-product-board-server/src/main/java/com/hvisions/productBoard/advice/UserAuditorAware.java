package com.hvisions.productBoard.advice;

import com.hvisions.common.consts.CookieConst;
import com.hvisions.common.consts.RedisConst;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.framework.client.AuthClient;
import com.hvisions.framework.dto.user.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>Title: UserAuditorAware</p>
 * <p>Description: 通过使用jpa的audit功能。需要配置h-visions.audit.mode=jpa</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
@Slf4j
@Component
public class UserAuditorAware implements AuditorAware<Integer> {
    @Resource
    HttpServletRequest request;
    /**
     * redis处理对象
     */
    @Resource
    StringRedisTemplate stringRedisTemplate;
    @Resource
    AuthClient authClient;


    /**
     * 获取当前用户信息
     * 如果token不是空，根据sessionId获取redis中的用户id,如果没有对应的header信息。或者其中出现了异常。用户id会被设置为0
     *
     * @return 当前用户信息
     */
    @Override
    public Optional<Integer> getCurrentAuditor() {
        String token;
        try {
            token = request.getHeader(CookieConst.AUTH_TOKEN);
        } catch (Exception e) {
            return Optional.of(0);
        }
        if (token == null) {
            return Optional.of(0);
        } else {
            try {
                String userIdStr = stringRedisTemplate.opsForValue()
                        .get(String.format(RedisConst.AUTH_REDIS_PREFIX, token));
                if (userIdStr == null) {
                    log.warn("获取audit用户信息错误，token对应的用户id为空," + token);
                    return Optional.of(0);
                }
                int userId = Integer.parseInt(userIdStr);
                return Optional.of(userId);
            } catch (Exception ex) {
                log.warn("audit用户id格式转换异常，请检查redis中存储的用户id格式是否异常," + token);
                return Optional.of(0);
            }
        }
    }

    /**
     * 全局得到当前登录用户信息
     * @return
     */
    public UserInfoDto getUser() {
        Integer userId = this.getCurrentAuditor().orElse(0);
        UserInfoDto userDTO = null;
        if (userId != 0) {
            ResultVO<List<UserInfoDto>> resultVO = authClient.getUserInfoByIds(Collections.singletonList(userId));
            log.info("==接口调用=={}", resultVO);
            if (resultVO.isSuccess()) {
                List<UserInfoDto> userInfoDtos = resultVO.getData();
                if (!userInfoDtos.isEmpty()) {
                    userDTO = userInfoDtos.get(0);
                }
            }
        }
        if(userDTO == null) {
            userDTO = new UserInfoDto();
            userDTO.setId(0);
            userDTO.setUserName("系统");
            userDTO.setUserAccount("admin");
        }
        return userDTO;
    }

}










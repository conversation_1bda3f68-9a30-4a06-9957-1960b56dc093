#dev配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: P<PERSON><PERSON>@0325
    url: ************************************************************************************************
  rabbitmq:
    host: **************
    port: 5672
    username: admin
    password: PH<PERSON>@0325
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000
h-visions:
  #当配置为true验证产出料是否与配置一致
  outPutMaterial: true
  #日志
  log:
    enable: false
  #物料批次号
  materialBatchNum: "(?<code>{8})"
  service-name: 工单管理服务
  #扩展数据库
  extend:
    schema: pms
  datasource: mysql

  work-order:
    lock:
      task: true
  swagger:
    api-url: http://localhost:9000/v2/api-docs;
    asciidoc-dir: ./build/asciidoc/
    markdown-dir: ./build/markdown/
    service-name: 订单服务
  change:
    crew: false
  order-type: "default"
  order-type-name: "默认工单类型"
server:
  port: 9080
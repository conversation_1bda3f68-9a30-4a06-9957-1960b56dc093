<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyQmHoseAssemblyOrderMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyQmHoseAssemblyOrder">
    <!--@mbg.generated-->
    <!--@Table sys_qms.hy_qm_hose_assembly_order-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="arm_type" jdbcType="VARCHAR" property="armType" />
    <result column="batch_num" jdbcType="VARCHAR" property="batchNum" />
    <result column="callipers" jdbcType="VARCHAR" property="callipers" />
    <result column="certificate" jdbcType="INTEGER" property="certificate" />
    <result column="customer_time" jdbcType="TIMESTAMP" property="customerTime" />
    <result column="customer_user_id" jdbcType="INTEGER" property="customerUserId" />
    <result column="customer_user_name" jdbcType="VARCHAR" property="customerUserName" />
    <result column="department_state" jdbcType="INTEGER" property="departmentState" />
    <result column="draw_num" jdbcType="VARCHAR" property="drawNum" />
    <result column="drawing_file_id" jdbcType="INTEGER" property="drawingFileId" />
    <result column="drawing_state" jdbcType="INTEGER" property="drawingState" />
    <result column="drawing_update_date_time" jdbcType="DATE" property="drawingUpdateDateTime" />
    <result column="drawing_version" jdbcType="VARCHAR" property="drawingVersion" />
    <result column="drawing_version_latest" jdbcType="VARCHAR" property="drawingVersionLatest" />
    <result column="drying_time" jdbcType="TIMESTAMP" property="dryingTime" />
    <result column="earlier_checkout" jdbcType="INTEGER" property="earlierCheckout" />
    <result column="final_user_id" jdbcType="INTEGER" property="finalUserId" />
    <result column="final_user_id2" jdbcType="INTEGER" property="finalUserId2" />
    <result column="final_user_name" jdbcType="VARCHAR" property="finalUserName" />
    <result column="final_user_name2" jdbcType="VARCHAR" property="finalUserName2" />
    <result column="final_user_time" jdbcType="TIMESTAMP" property="finalUserTime" />
    <result column="final_user_time2" jdbcType="TIMESTAMP" property="finalUserTime2" />
    <result column="gauge" jdbcType="VARCHAR" property="gauge" />
    <result column="hide_earlier_checkout" jdbcType="INTEGER" property="hideEarlierCheckout" />
    <result column="hose_call" jdbcType="VARCHAR" property="hoseCall" />
    <result column="is_activation" jdbcType="BIT" property="isActivation" />
    <result column="is_create_review_order" jdbcType="BIT" property="isCreateReviewOrder" />
    <result column="is_qu_submit" jdbcType="BIT" property="isQuSubmit" />
    <result column="is_return" jdbcType="BIT" property="isReturn" />
    <result column="is_save" jdbcType="BIT" property="isSave" />
    <result column="is_split" jdbcType="BIT" property="isSplit" />
    <result column="machine_type" jdbcType="VARCHAR" property="machineType" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_id" jdbcType="INTEGER" property="materialId" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_route_code" jdbcType="VARCHAR" property="materialRouteCode" />
    <result column="material_route_id" jdbcType="INTEGER" property="materialRouteId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="other_tests" jdbcType="VARCHAR" property="otherTests" />
    <result column="produce_time" jdbcType="TIMESTAMP" property="produceTime" />
    <result column="product_eigenvalue" jdbcType="VARCHAR" property="productEigenvalue" />
    <result column="product_model" jdbcType="VARCHAR" property="productModel" />
    <result column="quality_pass_count" jdbcType="INTEGER" property="qualityPassCount" />
    <result column="quality_state" jdbcType="INTEGER" property="qualityState" />
    <result column="quality_time" jdbcType="TIMESTAMP" property="qualityTime" />
    <result column="quality_type" jdbcType="INTEGER" property="qualityType" />
    <result column="quality_user_id" jdbcType="INTEGER" property="qualityUserId" />
    <result column="quality_user_name" jdbcType="VARCHAR" property="qualityUserName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ruler" jdbcType="VARCHAR" property="ruler" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="sale_order_line_id" jdbcType="INTEGER" property="saleOrderLineId" />
    <result column="side_contract" jdbcType="VARCHAR" property="sideContract" />
    <result column="storage_location" jdbcType="VARCHAR" property="storageLocation" />
    <result column="submit_order_code" jdbcType="VARCHAR" property="submitOrderCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="trial_accomplish_time" jdbcType="TIMESTAMP" property="trialAccomplishTime" />
    <result column="trial_accomplish_time2" jdbcType="TIMESTAMP" property="trialAccomplishTime2" />
    <result column="trial_pass_count" jdbcType="INTEGER" property="trialPassCount" />
    <result column="trial_time" jdbcType="TIMESTAMP" property="trialTime" />
    <result column="trial_time2" jdbcType="TIMESTAMP" property="trialTime2" />
    <result column="trial_user_id" jdbcType="INTEGER" property="trialUserId" />
    <result column="trial_user_id2" jdbcType="INTEGER" property="trialUserId2" />
    <result column="trial_user_name" jdbcType="VARCHAR" property="trialUserName" />
    <result column="trial_user_name2" jdbcType="VARCHAR" property="trialUserName2" />
    <result column="unqualified" jdbcType="INTEGER" property="unqualified" />
    <result column="work_order_code" jdbcType="VARCHAR" property="workOrderCode" />
    <result column="work_sub_order_code" jdbcType="VARCHAR" property="workSubOrderCode" />
    <result column="early_quality_time" jdbcType="TIMESTAMP" property="earlyQualityTime" />
    <result column="early_quality_user_id" jdbcType="INTEGER" property="earlyQualityUserId" />
    <result column="early_quality_user_name" jdbcType="VARCHAR" property="earlyQualityUserName" />
    <result column="research_order_code" jdbcType="VARCHAR" property="researchOrderCode" />
    <result column="research_order_line_id" jdbcType="INTEGER" property="researchOrderLineId" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="remark2" jdbcType="VARCHAR" property="remark2" />
    <result column="order_type_times" jdbcType="INTEGER" property="orderTypeTimes" />
    <result column="is_special" jdbcType="INTEGER" property="isSpecial" />
    <result column="quality_state_before" jdbcType="INTEGER" property="qualityStateBefore" />
    <result column="trial_un_pass_count" jdbcType="INTEGER" property="trialUnPassCount" />
    <result column="release_qty" jdbcType="INTEGER" property="releaseQty" />
    <result column="parent_work_sub_order_code" jdbcType="VARCHAR" property="parentWorkSubOrderCode" />
    <result column="is_urgency" jdbcType="INTEGER" property="isUrgency" />
    <result column="is_test" jdbcType="INTEGER" property="isTest" />
    <result column="test_times" jdbcType="INTEGER" property="testTimes" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, arm_type, batch_num, 
    callipers, certificate, customer_time, customer_user_id, customer_user_name, department_state, 
    draw_num, drawing_file_id, drawing_state, drawing_update_date_time, drawing_version, 
    drawing_version_latest, drying_time, earlier_checkout, final_user_id, final_user_id2, 
    final_user_name, final_user_name2, final_user_time, final_user_time2, gauge, hide_earlier_checkout, 
    hose_call, is_activation, is_create_review_order, is_qu_submit, is_return, is_save, 
    is_split, machine_type, material_code, material_id, material_name, material_route_code, 
    material_route_id, order_code, other_tests, produce_time, product_eigenvalue, product_model, 
    quality_pass_count, quality_state, quality_time, quality_type, quality_user_id, quality_user_name, 
    remark, ruler, sale_order_code, sale_order_line_id, side_contract, storage_location, 
    submit_order_code, supplier_name, total_count, trial_accomplish_time, trial_accomplish_time2, 
    trial_pass_count, trial_time, trial_time2, trial_user_id, trial_user_id2, trial_user_name, 
    trial_user_name2, unqualified, work_order_code, work_sub_order_code, early_quality_time, 
    early_quality_user_id, early_quality_user_name, research_order_code, research_order_line_id, 
    order_type, remark2, order_type_times, is_special, quality_state_before, trial_un_pass_count, 
    release_qty, parent_work_sub_order_code, is_urgency, is_test, test_times
  </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyPmMoldingWorkOrderMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyPmMoldingWorkOrder">
    <!--@mbg.generated-->
    <!--@Table sys_pms.hy_pm_molding_work_order-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime" />
    <result column="actual_start_time" jdbcType="TIMESTAMP" property="actualStartTime" />
    <result column="bom_code" jdbcType="VARCHAR" property="bomCode" />
    <result column="bom_id" jdbcType="INTEGER" property="bomId" />
    <result column="bom_name" jdbcType="VARCHAR" property="bomName" />
    <result column="bom_version" jdbcType="VARCHAR" property="bomVersion" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="creator_user_name" jdbcType="VARCHAR" property="creatorUserName" />
    <result column="deliver_state" jdbcType="INTEGER" property="deliverState" />
    <result column="finished_quantity" jdbcType="DECIMAL" property="finishedQuantity" />
    <result column="submitted_quantity" jdbcType="DECIMAL" property="submittedQuantity" />
    <result column="inner_pipe_color" jdbcType="VARCHAR" property="innerPipeColor" />
    <result column="material_batch_num" jdbcType="VARCHAR" property="materialBatchNum" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_eigenvalue" jdbcType="VARCHAR" property="materialEigenvalue" />
    <result column="material_id" jdbcType="INTEGER" property="materialId" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_route_id" jdbcType="INTEGER" property="materialRouteId" />
    <result column="plan_blank_quantity" jdbcType="DECIMAL" property="planBlankQuantity" />
    <result column="plan_end_time" jdbcType="TIMESTAMP" property="planEndTime" />
    <result column="plan_quantity" jdbcType="DECIMAL" property="planQuantity" />
    <result column="plan_start_time" jdbcType="TIMESTAMP" property="planStartTime" />
    <result column="product_group" jdbcType="VARCHAR" property="productGroup" />
    <result column="product_group_code" jdbcType="VARCHAR" property="productGroupCode" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="product_type_code" jdbcType="VARCHAR" property="productTypeCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="resin_raw_material_name" jdbcType="VARCHAR" property="resinRawMaterialName" />
    <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
    <result column="route_id" jdbcType="INTEGER" property="routeId" />
    <result column="route_name" jdbcType="VARCHAR" property="routeName" />
    <result column="route_update_time" jdbcType="TIMESTAMP" property="routeUpdateTime" />
    <result column="route_version" jdbcType="VARCHAR" property="routeVersion" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
    <result column="unit_symbol" jdbcType="VARCHAR" property="unitSymbol" />
    <result column="use_way" jdbcType="VARCHAR" property="useWay" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="deliver_state_name" jdbcType="VARCHAR" property="deliverStateName" />
    <result column="execute_param" jdbcType="VARCHAR" property="executeParam" />
    <result column="material_route_code" jdbcType="VARCHAR" property="materialRouteCode" />
    <result column="material_route_name" jdbcType="VARCHAR" property="materialRouteName" />
    <result column="material_route_update_time" jdbcType="TIMESTAMP" property="materialRouteUpdateTime" />
    <result column="material_route_version" jdbcType="VARCHAR" property="materialRouteVersion" />
    <result column="product_batch_number" jdbcType="VARCHAR" property="productBatchNumber" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_eigenvalue" jdbcType="VARCHAR" property="productEigenvalue" />
    <result column="product_group_name" jdbcType="VARCHAR" property="productGroupName" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="product_model" jdbcType="VARCHAR" property="productModel" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_type_name" jdbcType="VARCHAR" property="productTypeName" />
    <result column="resin_name" jdbcType="VARCHAR" property="resinName" />
    <result column="route_rule" jdbcType="VARCHAR" property="routeRule" />
    <result column="state_name" jdbcType="VARCHAR" property="stateName" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="unit_name" jdbcType="VARCHAR" property="unitName" />
    <result column="use_way_name" jdbcType="VARCHAR" property="useWayName" />
    <result column="work_guide_book" jdbcType="VARCHAR" property="workGuideBook" />
    <result column="drawing_num" jdbcType="VARCHAR" property="drawingNum" />
    <result column="output_mblnr" jdbcType="VARCHAR" property="outputMblnr" />
    <result column="output_msg" jdbcType="VARCHAR" property="outputMsg" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="consume_submit_state" jdbcType="INTEGER" property="consumeSubmitState" />
    <result column="submitted_weight" jdbcType="DECIMAL" property="submittedWeight" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, actual_end_time, 
    actual_start_time, bom_code, bom_id, bom_name, bom_version, code, creator_user_name, 
    deliver_state, finished_quantity, submitted_quantity, inner_pipe_color, material_batch_num, 
    material_code, material_eigenvalue, material_id, material_name, material_route_id, 
    plan_blank_quantity, plan_end_time, plan_quantity, plan_start_time, product_group, 
    product_group_code, product_type, product_type_code, remark, resin_raw_material_name, 
    route_code, route_id, route_name, route_update_time, route_version, `state`, `type`, 
    unit_code, unit_symbol, use_way, color, creator_name, deliver_state_name, execute_param, 
    material_route_code, material_route_name, material_route_update_time, material_route_version, 
    product_batch_number, product_code, product_eigenvalue, product_group_name, product_id, 
    product_model, product_name, product_type_name, resin_name, route_rule, state_name, 
    type_name, unit_name, use_way_name, work_guide_book, drawing_num, output_mblnr, output_msg, 
    sap_code, consume_submit_state, submitted_weight
  </sql>
</mapper>
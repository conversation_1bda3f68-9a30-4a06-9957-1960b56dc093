<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyPmPkgOrderLineMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyPmPkgOrderLine">
    <!--@mbg.generated-->
    <!--@Table sys_pms.hy_pm_pkg_order_line-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_eigenvalue" jdbcType="VARCHAR" property="materialEigenvalue" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_name_desc" jdbcType="VARCHAR" property="materialNameDesc" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="product_model" jdbcType="VARCHAR" property="productModel" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="sale_batch_no" jdbcType="VARCHAR" property="saleBatchNo" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="submit_order_code" jdbcType="VARCHAR" property="submitOrderCode" />
    <result column="unit_quantity" jdbcType="INTEGER" property="unitQuantity" />
    <result column="work_sub_order_code" jdbcType="VARCHAR" property="workSubOrderCode" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, batch_no, code, material_code, 
    material_eigenvalue, material_name, material_name_desc, memo, product_model, quantity, 
    sale_batch_no, sale_order_code, sap_code, submit_order_code, unit_quantity, work_sub_order_code, 
    unit_price
  </sql>
</mapper>
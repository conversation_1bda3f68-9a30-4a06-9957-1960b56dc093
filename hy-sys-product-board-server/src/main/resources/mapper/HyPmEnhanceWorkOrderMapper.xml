<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyPmEnhanceWorkOrderMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyPmEnhanceWorkOrder">
    <!--@mbg.generated-->
    <!--@Table sys_pms.hy_pm_enhance_work_order-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime" />
    <result column="actual_start_time" jdbcType="TIMESTAMP" property="actualStartTime" />
    <result column="bom_code" jdbcType="VARCHAR" property="bomCode" />
    <result column="bom_id" jdbcType="INTEGER" property="bomId" />
    <result column="bom_name" jdbcType="VARCHAR" property="bomName" />
    <result column="bom_version" jdbcType="VARCHAR" property="bomVersion" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="complete_step" jdbcType="INTEGER" property="completeStep" />
    <result column="creator_user_name" jdbcType="VARCHAR" property="creatorUserName" />
    <result column="deliver_state" jdbcType="INTEGER" property="deliverState" />
    <result column="enhance_modality" jdbcType="VARCHAR" property="enhanceModality" />
    <result column="finished_quantity" jdbcType="DECIMAL" property="finishedQuantity" />
    <result column="inner_pipe_color" jdbcType="VARCHAR" property="innerPipeColor" />
    <result column="material_batch_num" jdbcType="VARCHAR" property="materialBatchNum" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_desc" jdbcType="VARCHAR" property="materialDesc" />
    <result column="material_eigenvalue" jdbcType="VARCHAR" property="materialEigenvalue" />
    <result column="material_id" jdbcType="INTEGER" property="materialId" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_route_id" jdbcType="INTEGER" property="materialRouteId" />
    <result column="plan_end_time" jdbcType="TIMESTAMP" property="planEndTime" />
    <result column="plan_quantity" jdbcType="DECIMAL" property="planQuantity" />
    <result column="plan_start_time" jdbcType="TIMESTAMP" property="planStartTime" />
    <result column="print_count" jdbcType="INTEGER" property="printCount" />
    <result column="product_group" jdbcType="VARCHAR" property="productGroup" />
    <result column="product_group_code" jdbcType="VARCHAR" property="productGroupCode" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="product_type_code" jdbcType="VARCHAR" property="productTypeCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="resin_raw_material_name" jdbcType="VARCHAR" property="resinRawMaterialName" />
    <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
    <result column="route_id" jdbcType="INTEGER" property="routeId" />
    <result column="route_name" jdbcType="VARCHAR" property="routeName" />
    <result column="route_update_time" jdbcType="TIMESTAMP" property="routeUpdateTime" />
    <result column="route_version" jdbcType="VARCHAR" property="routeVersion" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="steel_wire_name" jdbcType="VARCHAR" property="steelWireName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
    <result column="unit_symbol" jdbcType="VARCHAR" property="unitSymbol" />
    <result column="use_way" jdbcType="VARCHAR" property="useWay" />
    <result column="working_instruction" jdbcType="VARCHAR" property="workingInstruction" />
    <result column="pipe_no" jdbcType="VARCHAR" property="pipeNo" />
    <result column="output_mblnr" jdbcType="VARCHAR" property="outputMblnr" />
    <result column="output_msg" jdbcType="VARCHAR" property="outputMsg" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="wire_batch" jdbcType="VARCHAR" property="wireBatch" />
    <result column="consume_submit_state" jdbcType="INTEGER" property="consumeSubmitState" />
    <result column="wire_batch_json" jdbcType="LONGVARCHAR" property="wireBatchJson" />
    <result column="pipe_num" jdbcType="INTEGER" property="pipeNum" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, actual_end_time, 
    actual_start_time, bom_code, bom_id, bom_name, bom_version, code, complete_step, 
    creator_user_name, deliver_state, enhance_modality, finished_quantity, inner_pipe_color, 
    material_batch_num, material_code, material_desc, material_eigenvalue, material_id, 
    material_name, material_route_id, plan_end_time, plan_quantity, plan_start_time, 
    print_count, product_group, product_group_code, product_type, product_type_code, 
    remark, resin_raw_material_name, route_code, route_id, route_name, route_update_time, 
    route_version, `state`, steel_wire_name, `type`, unit_code, unit_symbol, use_way, 
    working_instruction, pipe_no, output_mblnr, output_msg, sap_code, wire_batch, consume_submit_state, 
    wire_batch_json, pipe_num
  </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.PmAssemblyWorkOrderMapper">

    <select id="selectListByBiAssemblyKeyOrderReq" resultType="com.hvisions.productBoard.entity.PmAssemblyWorkOrder">
        SELECT t1.*
        FROM sys_pms.hy_pm_assembly_work_order t1
        WHERE t1.state = 10 AND t1.is_urgency = true
    </select>

    <select id="selectListByStateIngAndLimitSize" resultType="com.hvisions.productBoard.entity.PmAssemblyWorkOrder">
        SELECT t1.*
        FROM sys_pms.hy_pm_assembly_work_order t1
        WHERE t1.state = 10 AND (t1.is_urgency = false OR t1.is_urgency IS NULL)
        LIMIT 1, #{limitSize}
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.PmAssemblyWorkSubOrderMapper">

    <select id="selectList1" resultType="com.hvisions.productBoard.entity.PmAssemblyWorkSubOrder">
        SELECT t1.*
        FROM sys_pms.hy_pms_work_sub_order t1
        WHERE 1 = 1
            AND t1.state = 10
        <if test="saleOrderCode != null and saleOrderCode != ''">
            AND t1.sale_order_code LIKE CONCAT('%', #{saleOrderCode}, '%')
        </if>
        <if test="workOrderCode != null and workOrderCode != ''">
            AND t1.work_order_code LIKE CONCAT('%', #{workOrderCode}, '%')
        </if>
        <if test="workSubOrderCode != null and workSubOrderCode != ''">
            AND t1.code LIKE CONCAT('%', #{workSubOrderCode}, '%')
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND t1.material_code LIKE CONCAT('%', #{materialCode}, '%')
        </if>
        <if test="productModel != null and productModel != ''">
            AND t1.product_model LIKE CONCAT('%', #{productModel}, '%')
        </if>
        <if test="eigenvalue != null and eigenvalue != ''">
            AND t1.eigenvalue LIKE CONCAT('%', #{eigenvalue}, '%')
        </if>
        <if test="isUrgent != null">
            AND t1.is_urgency = #{isUrgent}
        </if>
    </select>

    <select id="selectList2" resultType="com.hvisions.productBoard.entity.PmAssemblyWorkSubOrder">
        SELECT t1.*
        FROM sys_pms.hy_pms_work_sub_order t1
        LEFT JOIN sys_pms.hy_pms_work_sub_order_operation_output t2 ON t1.code = t2.work_sub_order_code
        WHERE 1 = 1
            AND t1.state = 10
            AND t2.operation_name = #{operationName}
        <if test="saleOrderCode != null and saleOrderCode != ''">
            AND t1.sale_order_code LIKE CONCAT('%', #{saleOrderCode}, '%')
        </if>
        <if test="workOrderCode != null and workOrderCode != ''">
            AND t1.work_order_code LIKE CONCAT('%', #{workOrderCode}, '%')
        </if>
        <if test="workSubOrderCode != null and workSubOrderCode != ''">
            AND t1.code LIKE CONCAT('%', #{workSubOrderCode}, '%')
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND t1.material_code LIKE CONCAT('%', #{materialCode}, '%')
        </if>
        <if test="productModel != null and productModel != ''">
            AND t1.product_model LIKE CONCAT('%', #{productModel}, '%')
        </if>
        <if test="eigenvalue != null and eigenvalue != ''">
            AND t1.eigenvalue LIKE CONCAT('%', #{eigenvalue}, '%')
        </if>
        <if test="isUrgent != null">
            AND t1.is_urgency = #{isUrgent}
        </if>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyWmsCompleteDeliverOrderMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyWmsCompleteDeliverOrder">
    <!--@mbg.generated-->
    <!--@Table sys_wms.hy_wms_complete_deliver_order-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="actual_quantity" jdbcType="DECIMAL" property="actualQuantity" />
    <result column="arm_type" jdbcType="VARCHAR" property="armType" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="exception_msg" jdbcType="VARCHAR" property="exceptionMsg" />
    <result column="finish_date" jdbcType="TIMESTAMP" property="finishDate" />
    <result column="from_location" jdbcType="VARCHAR" property="fromLocation" />
    <result column="from_location_code" jdbcType="VARCHAR" property="fromLocationCode" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="pkg_order_code" jdbcType="VARCHAR" property="pkgOrderCode" />
    <result column="plan_count" jdbcType="DECIMAL" property="planCount" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="submit_times" jdbcType="INTEGER" property="submitTimes" />
    <result column="target_location" jdbcType="VARCHAR" property="targetLocation" />
    <result column="target_location_code" jdbcType="VARCHAR" property="targetLocationCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="sap_order_code" jdbcType="VARCHAR" property="sapOrderCode" />
    <result column="submit_state" jdbcType="INTEGER" property="submitState" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="material_conver_code" jdbcType="VARCHAR" property="materialConverCode" />
    <result column="biz_order_type" jdbcType="INTEGER" property="bizOrderType" />
    <result column="biz_order_type_name" jdbcType="VARCHAR" property="bizOrderTypeName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, actual_quantity, 
    arm_type, code, customer_name, exception_msg, finish_date, from_location, from_location_code, 
    memo, pkg_order_code, plan_count, product_name, sale_order_code, `state`, submit_times, 
    target_location, target_location_code, user_name, sap_order_code, submit_state, department, 
    material_conver_code, biz_order_type, biz_order_type_name
  </sql>
</mapper>
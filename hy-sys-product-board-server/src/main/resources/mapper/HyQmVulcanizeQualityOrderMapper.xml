<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyQmVulcanizeQualityOrderMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyQmVulcanizeQualityOrder">
    <!--@mbg.generated-->
    <!--@Table sys_qms.hy_qm_vulcanize_quality_order-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="arm_type" jdbcType="VARCHAR" property="armType" />
    <result column="batch_num" jdbcType="VARCHAR" property="batchNum" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="custom_company" jdbcType="VARCHAR" property="customCompany" />
    <result column="machine_type" jdbcType="VARCHAR" property="machineType" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_eigenvalue" jdbcType="VARCHAR" property="productEigenvalue" />
    <result column="product_model" jdbcType="VARCHAR" property="productModel" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="production_type_name" jdbcType="VARCHAR" property="productionTypeName" />
    <result column="quality_pass_count" jdbcType="INTEGER" property="qualityPassCount" />
    <result column="quality_time" jdbcType="TIMESTAMP" property="qualityTime" />
    <result column="quality_user_id" jdbcType="INTEGER" property="qualityUserId" />
    <result column="quality_user_name" jdbcType="VARCHAR" property="qualityUserName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="vulcanize_material" jdbcType="VARCHAR" property="vulcanizeMaterial" />
    <result column="vulcanize_state" jdbcType="INTEGER" property="vulcanizeState" />
    <result column="vulcanize_times" jdbcType="INTEGER" property="vulcanizeTimes" />
    <result column="work_order_code" jdbcType="VARCHAR" property="workOrderCode" />
    <result column="work_sub_order_code" jdbcType="VARCHAR" property="workSubOrderCode" />
    <result column="split_tag" jdbcType="BIT" property="splitTag" />
    <result column="work_order_id" jdbcType="INTEGER" property="workOrderId" />
    <result column="drawing_state" jdbcType="INTEGER" property="drawingState" />
    <result column="drawing_update_date_time" jdbcType="DATE" property="drawingUpdateDateTime" />
    <result column="drawing_version" jdbcType="VARCHAR" property="drawingVersion" />
    <result column="drawing_version_latest" jdbcType="VARCHAR" property="drawingVersionLatest" />
    <result column="material_batch_num" jdbcType="VARCHAR" property="materialBatchNum" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_id" jdbcType="INTEGER" property="materialId" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_route_code" jdbcType="VARCHAR" property="materialRouteCode" />
    <result column="material_route_id" jdbcType="INTEGER" property="materialRouteId" />
    <result column="material_route_name" jdbcType="VARCHAR" property="materialRouteName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, arm_type, batch_num, 
    creator_name, custom_company, machine_type, order_code, product_code, product_eigenvalue, 
    product_model, product_name, production_type_name, quality_pass_count, quality_time, 
    quality_user_id, quality_user_name, remark, sale_order_code, supplier_name, total_count, 
    vulcanize_material, vulcanize_state, vulcanize_times, work_order_code, work_sub_order_code, 
    split_tag, work_order_id, drawing_state, drawing_update_date_time, drawing_version, 
    drawing_version_latest, material_batch_num, material_code, material_id, material_name, 
    material_route_code, material_route_id, material_route_name
  </sql>
</mapper>
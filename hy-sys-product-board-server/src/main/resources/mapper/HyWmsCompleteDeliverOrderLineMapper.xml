<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyWmsCompleteDeliverOrderLineMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyWmsCompleteDeliverOrderLine">
    <!--@mbg.generated-->
    <!--@Table sys_wms.hy_wms_complete_deliver_order_line-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="actual_quantity" jdbcType="DECIMAL" property="actualQuantity" />
    <result column="arm_type" jdbcType="VARCHAR" property="armType" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_eigenvalue" jdbcType="VARCHAR" property="materialEigenvalue" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_name_desc" jdbcType="VARCHAR" property="materialNameDesc" />
    <result column="pkg_order_code" jdbcType="VARCHAR" property="pkgOrderCode" />
    <result column="pkg_order_line_id" jdbcType="INTEGER" property="pkgOrderLineId" />
    <result column="product_model" jdbcType="VARCHAR" property="productModel" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="sale_batch_no" jdbcType="VARCHAR" property="saleBatchNo" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="work_sub_order_code" jdbcType="VARCHAR" property="workSubOrderCode" />
    <result column="sap_work_order_code" jdbcType="VARCHAR" property="sapWorkOrderCode" />
    <result column="finish_date" jdbcType="TIMESTAMP" property="finishDate" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="material_conver_code" jdbcType="VARCHAR" property="materialConverCode" />
    <result column="biz_order_type" jdbcType="INTEGER" property="bizOrderType" />
    <result column="biz_order_type_name" jdbcType="VARCHAR" property="bizOrderTypeName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, actual_quantity, 
    arm_type, batch_no, code, material_code, material_eigenvalue, material_name, material_name_desc, 
    pkg_order_code, pkg_order_line_id, product_model, quantity, sale_batch_no, sale_order_code, 
    unit, work_sub_order_code, sap_work_order_code, finish_date, `state`, department, 
    material_conver_code, biz_order_type, biz_order_type_name
  </sql>
</mapper>
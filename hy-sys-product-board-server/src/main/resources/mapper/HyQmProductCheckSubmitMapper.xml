<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyQmProductCheckSubmitMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyQmProductCheckSubmit">
    <!--@mbg.generated-->
    <!--@Table sys_qms.hy_qm_product_check_submit-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="arm_type" jdbcType="VARCHAR" property="armType" />
    <result column="bom_id" jdbcType="INTEGER" property="bomId" />
    <result column="contract" jdbcType="VARCHAR" property="contract" />
    <result column="flag" jdbcType="BIT" property="flag" />
    <result column="inspection_quantity" jdbcType="INTEGER" property="inspectionQuantity" />
    <result column="inspector" jdbcType="VARCHAR" property="inspector" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="pass_rate" jdbcType="DECIMAL" property="passRate" />
    <result column="print_times" jdbcType="INTEGER" property="printTimes" />
    <result column="print_type" jdbcType="VARCHAR" property="printType" />
    <result column="produce_time" jdbcType="TIMESTAMP" property="produceTime" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="qualified_quantity" jdbcType="INTEGER" property="qualifiedQuantity" />
    <result column="quality" jdbcType="INTEGER" property="quality" />
    <result column="quality_conclusion" jdbcType="VARCHAR" property="qualityConclusion" />
    <result column="quality_state" jdbcType="INTEGER" property="qualityState" />
    <result column="quality_time" jdbcType="TIMESTAMP" property="qualityTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="side_contract" jdbcType="VARCHAR" property="sideContract" />
    <result column="storage_location" jdbcType="VARCHAR" property="storageLocation" />
    <result column="submit_order_code" jdbcType="VARCHAR" property="submitOrderCode" />
    <result column="submit_quantity" jdbcType="INTEGER" property="submitQuantity" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="supervision_order" jdbcType="VARCHAR" property="supervisionOrder" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="technical_ask" jdbcType="VARCHAR" property="technicalAsk" />
    <result column="try_code" jdbcType="VARCHAR" property="tryCode" />
    <result column="split_tag" jdbcType="BIT" property="splitTag" />
    <result column="research_order_code" jdbcType="VARCHAR" property="researchOrderCode" />
    <result column="inspect_date_summary" jdbcType="VARCHAR" property="inspectDateSummary" />
    <result column="product_model" jdbcType="VARCHAR" property="productModel" />
    <result column="regist_state" jdbcType="INTEGER" property="registState" />
    <result column="test_times" jdbcType="INTEGER" property="testTimes" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, arm_type, bom_id, 
    contract, flag, inspection_quantity, inspector, order_code, pass_rate, print_times, 
    print_type, produce_time, product_code, product_name, qualified_quantity, quality, 
    quality_conclusion, quality_state, quality_time, remark, sale_order_code, side_contract, 
    storage_location, submit_order_code, submit_quantity, submit_time, supervision_order, 
    supplier_name, technical_ask, try_code, split_tag, research_order_code, inspect_date_summary, 
    product_model, regist_state, test_times
  </sql>
</mapper>
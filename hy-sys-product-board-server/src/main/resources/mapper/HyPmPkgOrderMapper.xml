<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyPmPkgOrderMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyPmPkgOrder">
    <!--@mbg.generated-->
    <!--@Table sys_pms.hy_pm_pkg_order-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="complete_deliver_order_code" jdbcType="VARCHAR" property="completeDeliverOrderCode" />
    <result column="contract_num" jdbcType="VARCHAR" property="contractNum" />
    <result column="customer" jdbcType="VARCHAR" property="customer" />
    <result column="flag" jdbcType="BIT" property="flag" />
    <result column="pkg_date" jdbcType="TIMESTAMP" property="pkgDate" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_time" jdbcType="TIMESTAMP" property="productTime" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="soldier_check" jdbcType="VARCHAR" property="soldierCheck" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="submit_order_code" jdbcType="VARCHAR" property="submitOrderCode" />
    <result column="submit_state" jdbcType="INTEGER" property="submitState" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="consume_submit_state" jdbcType="INTEGER" property="consumeSubmitState" />
    <result column="submit_date" jdbcType="TIMESTAMP" property="submitDate" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
    <result column="plan_end_time" jdbcType="TIMESTAMP" property="planEndTime" />
    <result column="plan_start_time" jdbcType="TIMESTAMP" property="planStartTime" />
    <result column="is_research" jdbcType="INTEGER" property="isResearch" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, code, complete_deliver_order_code, 
    contract_num, customer, flag, pkg_date, product_name, product_time, quantity, reason, 
    sale_order_code, soldier_check, `state`, submit_order_code, submit_state, unit, consume_submit_state, 
    submit_date, sap_code, plan_end_time, plan_start_time, is_research
  </sql>
</mapper>
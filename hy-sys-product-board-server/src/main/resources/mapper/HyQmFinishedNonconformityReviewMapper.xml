<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyQmFinishedNonconformityReviewMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyQmFinishedNonconformityReview">
    <!--@mbg.generated-->
    <!--@Table sys_qms.hy_qm_finished_nonconformity_review-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="batch_num" jdbcType="VARCHAR" property="batchNum" />
    <result column="drawing_no" jdbcType="VARCHAR" property="drawingNo" />
    <result column="examination" jdbcType="VARCHAR" property="examination" />
    <result column="initiate_time" jdbcType="TIMESTAMP" property="initiateTime" />
    <result column="nonconformity_count" jdbcType="INTEGER" property="nonconformityCount" />
    <result column="nonconformity_grade" jdbcType="INTEGER" property="nonconformityGrade" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_eigenvalue" jdbcType="VARCHAR" property="productEigenvalue" />
    <result column="product_model" jdbcType="VARCHAR" property="productModel" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="quality_class" jdbcType="INTEGER" property="qualityClass" />
    <result column="quality_state" jdbcType="INTEGER" property="qualityState" />
    <result column="quality_time" jdbcType="TIMESTAMP" property="qualityTime" />
    <result column="quality_type" jdbcType="VARCHAR" property="qualityType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="submit_order_code" jdbcType="VARCHAR" property="submitOrderCode" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="work_order_code" jdbcType="VARCHAR" property="workOrderCode" />
    <result column="work_sub_order_code" jdbcType="VARCHAR" property="workSubOrderCode" />
    <result column="submit_product_name" jdbcType="VARCHAR" property="submitProductName" />
    <result column="print_no" jdbcType="VARCHAR" property="printNo" />
    <result column="version" jdbcType="VARCHAR" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, batch_num, drawing_no, 
    examination, initiate_time, nonconformity_count, nonconformity_grade, order_code, 
    product_code, product_eigenvalue, product_model, product_name, quality_class, quality_state, 
    quality_time, quality_type, remark, sale_order_code, submit_order_code, total_count, 
    work_order_code, work_sub_order_code, submit_product_name, print_no, version
  </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.LmMapper">

    <select id="selectCountOfWorkingContract" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_lims2.contract c
        WHERE c.status IN (1, 2, 3)
    </select>

    <select id="selectQuantityOfWorkingContract" resultType="java.math.BigDecimal">
        SELECT SUM(s.sample_quantity)
        FROM sys_lims2.contract c
        LEFT JOIN sys_lims2.sample s ON c.id = s.contract_id
        WHERE c.status IN (1, 2, 3)
    </select>

    <select id="selectCountOfFinishedContractBetween" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_lims2.contract c
        WHERE c.status = 5 AND c.finish_date BETWEEN #{start} AND #{end}
    </select>

    <select id="selectQuantityOfFinishedContractBetween" resultType="java.math.BigDecimal">
        SELECT SUM(s.sample_quantity)
        FROM sys_lims2.contract c
        LEFT JOIN sys_lims2.sample s ON c.id = s.contract_id
        WHERE c.status = 5 AND c.finish_date BETWEEN #{start} AND #{end}
    </select>

    <resultMap id="contractResultMap" type="com.hvisions.productBoard.dto.ContractDTO">
        <id property="id" column="id" />
        <result property="finishDate" column="finish_date" />
        <result property="quantity" column="quantity" />
    </resultMap>

    <select id="selectListOfFinishedContractBetween2" resultMap="contractResultMap">
        SELECT c.id, c.finish_date
        FROM sys_lims2.contract c
        WHERE c.status = 5
          AND c.finish_date BETWEEN #{start} AND #{end}
    </select>

    <select id="selectListOfFinishedContractBetween3" resultMap="contractResultMap">
        SELECT c.id, c.finish_date, SUM(s.sample_quantity) quantity
        FROM sys_lims2.contract c
        LEFT JOIN sys_lims2.sample s ON c.id = s.contract_id
        WHERE c.status = 5
          AND c.finish_date BETWEEN #{start} AND #{end}
        GROUP BY c.id
    </select>

</mapper>
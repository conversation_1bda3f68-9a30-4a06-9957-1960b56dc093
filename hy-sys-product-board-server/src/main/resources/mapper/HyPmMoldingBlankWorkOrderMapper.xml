<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyPmMoldingBlankWorkOrderMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyPmMoldingBlankWorkOrder">
    <!--@mbg.generated-->
    <!--@Table sys_pms.hy_pm_molding_blank_work_order-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="actual_end_date_time" jdbcType="TIMESTAMP" property="actualEndDateTime" />
    <result column="actual_quantity" jdbcType="DECIMAL" property="actualQuantity" />
    <result column="actual_start_date_time" jdbcType="TIMESTAMP" property="actualStartDateTime" />
    <result column="blank_work_plan_id" jdbcType="INTEGER" property="blankWorkPlanId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="execute_param" jdbcType="VARCHAR" property="executeParam" />
    <result column="is_disabled" jdbcType="INTEGER" property="isDisabled" />
    <result column="is_loss" jdbcType="VARCHAR" property="isLoss" />
    <result column="operator_account" jdbcType="VARCHAR" property="operatorAccount" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="plan_date_time" jdbcType="TIMESTAMP" property="planDateTime" />
    <result column="plan_quantity" jdbcType="DECIMAL" property="planQuantity" />
    <result column="product_batch_number" jdbcType="VARCHAR" property="productBatchNumber" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_eigenvalue" jdbcType="VARCHAR" property="productEigenvalue" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="push_record_code" jdbcType="VARCHAR" property="pushRecordCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="route_rule" jdbcType="VARCHAR" property="routeRule" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="shift_code" jdbcType="VARCHAR" property="shiftCode" />
    <result column="shift_id" jdbcType="INTEGER" property="shiftId" />
    <result column="shift_name" jdbcType="VARCHAR" property="shiftName" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="state_name" jdbcType="VARCHAR" property="stateName" />
    <result column="team_code" jdbcType="VARCHAR" property="teamCode" />
    <result column="team_id" jdbcType="INTEGER" property="teamId" />
    <result column="team_name" jdbcType="VARCHAR" property="teamName" />
    <result column="trial_state" jdbcType="INTEGER" property="trialState" />
    <result column="trial_state_name" jdbcType="VARCHAR" property="trialStateName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="unit_symbol" jdbcType="VARCHAR" property="unitSymbol" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="uuid2" jdbcType="VARCHAR" property="uuid2" />
    <result column="work_guide_book" jdbcType="VARCHAR" property="workGuideBook" />
    <result column="work_order_code" jdbcType="VARCHAR" property="workOrderCode" />
    <result column="work_order_id" jdbcType="INTEGER" property="workOrderId" />
    <result column="sort_seq" jdbcType="INTEGER" property="sortSeq" />
    <result column="plan_black_material_bottle_quantity" jdbcType="DECIMAL" property="planBlackMaterialBottleQuantity" />
    <result column="plan_main_material_bottle_quantity" jdbcType="DECIMAL" property="planMainMaterialBottleQuantity" />
    <result column="plan_minor_material_bottle_quantity" jdbcType="DECIMAL" property="planMinorMaterialBottleQuantity" />
    <result column="inspection_order_code" jdbcType="VARCHAR" property="inspectionOrderCode" />
    <result column="is_inspection_ordered" jdbcType="INTEGER" property="isInspectionOrdered" />
    <result column="pei_liao_order_code" jdbcType="VARCHAR" property="peiLiaoOrderCode" />
    <result column="drawing_num" jdbcType="VARCHAR" property="drawingNum" />
    <result column="measure_appliance" jdbcType="VARCHAR" property="measureAppliance" />
    <result column="caliper" jdbcType="VARCHAR" property="caliper" />
    <result column="tapeline" jdbcType="VARCHAR" property="tapeline" />
    <result column="process_state" jdbcType="INTEGER" property="processState" />
    <result column="process_state_name" jdbcType="VARCHAR" property="processStateName" />
    <result column="drawing_version" jdbcType="VARCHAR" property="drawingVersion" />
    <result column="material_route_id" jdbcType="INTEGER" property="materialRouteId" />
    <result column="batch_pipe_no" jdbcType="VARCHAR" property="batchPipeNo" />
    <result column="batch_drawing_num" jdbcType="VARCHAR" property="batchDrawingNum" />
    <result column="inspection_state" jdbcType="INTEGER" property="inspectionState" />
    <result column="cancel_process" jdbcType="INTEGER" property="cancelProcess" />
    <result column="is_trial_produce" jdbcType="INTEGER" property="isTrialProduce" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, actual_end_date_time, 
    actual_quantity, actual_start_date_time, blank_work_plan_id, code, color, execute_param, 
    is_disabled, is_loss, operator_account, operator_id, operator_name, plan_date_time, 
    plan_quantity, product_batch_number, product_code, product_eigenvalue, product_name, 
    push_record_code, remark, route_rule, seq, shift_code, shift_id, shift_name, `state`, 
    state_name, team_code, team_id, team_name, trial_state, trial_state_name, `type`, 
    type_name, unit_symbol, uuid, uuid2, work_guide_book, work_order_code, work_order_id, 
    sort_seq, plan_black_material_bottle_quantity, plan_main_material_bottle_quantity, 
    plan_minor_material_bottle_quantity, inspection_order_code, is_inspection_ordered, 
    pei_liao_order_code, drawing_num, measure_appliance, caliper, tapeline, process_state, 
    process_state_name, drawing_version, material_route_id, batch_pipe_no, batch_drawing_num, 
    inspection_state, cancel_process, is_trial_produce
  </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hvisions</groupId>
    <artifactId>hy-sys-product-board</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>hy-sys-product-board</name>
    <description>HVisions-micro-service-sale</description>
    <repositories>
        <repository>
            <id>nexus</id>
            <name>Nexus Repository</name>
            <url>http://**************:8022/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <!--snapshots默认是关闭的,需要开启  -->
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version> <!-- lookup parent from repository -->
    </parent>
    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://**************:8022/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshot</id>
            <url>http://**************:8022/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <!--关闭了测试。如果需要在编译过程中测试，可以删除此项配置-->
        <maven.test.skip>true</maven.test.skip>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring cloud alibaba 版本 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2.2.6.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.5</version>
            </dependency>

            <dependency>
                <groupId>com.hvisions</groupId>
                <artifactId>common-microservice</artifactId>
                <version>3.0.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                    <!-- <exclusion> -->
                    <!--     <groupId>com.github.pagehelper</groupId> -->
                    <!--     <artifactId>pagehelper-spring-boot-starter</artifactId> -->
                    <!-- </exclusion> -->
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.3</version>
                <optional>true</optional>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>3.5.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <scm>
        <developerConnection>scm:git:http://**************:8010/hipermatic/sale.git</developerConnection>
        <tag>v@{project-version}</tag>
    </scm>
    <build>
        <plugins>
            <!-- 发布插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <arguments>-DskipTests</arguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>2.4</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <skipSource>true</skipSource>
                </configuration>
            </plugin>
        </plugins>
    </build>


    <modules>
        <module>hy-sys-product-board-server</module>
        <module>hy-sys-product-board-client</module>
        <module>hy-sys-product-board-common</module>
    </modules>
</project>

package com.hvisions.productBoard;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@Data
@ApiModel(description = "底层DTO")
public class SysBaseDTO {

    /**
     * 主键
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键")
    protected Integer id;

    /**
     * 创建时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建时间", readOnly = true)
    protected Date createTime;

    /**
     * 修改时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新时间", readOnly = true)
    protected Date updateTime;

    /**
     * 创建人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建用户Id", readOnly = true)
    protected Integer creatorId;

    /**
     * 修改人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新用户Id", readOnly = true)
    protected Integer updaterId;

    /**
     * 用于后续saas服务租户字段
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "租户id", readOnly = true)
    protected String siteNum;
}

package com.hvisions.productBoard.materialStocks.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;


@Data
@ApiModel(value = "库存查询对象")
public class MaterialStockQueryDTO {

    @ApiModelProperty(value = "物料编码列表")
    private List<String> materialCodes = Collections.singletonList("80385571");

    @ApiModelProperty(value = "物料类型列表")
    private List<String> materialTypeCodes = Arrays.asList("Z101", "Z201", "Z202");
}

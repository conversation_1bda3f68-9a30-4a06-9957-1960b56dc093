package com.hvisions.productBoard.materialStocks.vo;

import com.hvisions.productBoard.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;


@ApiModel(description = "库存")
@EqualsAndHashCode(callSuper = true)
@Data
public class WmsMaterialStocksVO extends SysBaseDTO {
    @ApiModelProperty(value = "物料类型")
    private String materialTypeCode;
    @ApiModelProperty(value = "物料类型名称")
    private String materialTypeName;
    @ApiModelProperty(value = "物料分组")
    private String materialGroupCode;
    @ApiModelProperty(value = "物料分组名称")
    private String materialGroupName;
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    @ApiModelProperty(value = "物料图号")
    private String drawingNum;
    @ApiModelProperty(value = "物料规格")
    private String materialEigenvalue;
    @ApiModelProperty(value = "物料简称")
    private String materialDescribe;
    @ApiModelProperty(value = "物料单位")
    private String materialUnit;
    @ApiModelProperty(value = "物料单位中文")
    private String materialUnitName;
    @ApiModelProperty(value = "请购在途数量")
    private BigDecimal purchaseApplyQuantity;
    @ApiModelProperty(value = "请购在途共享数量")
    private BigDecimal purchaseApplyPublicQuantity;
    @ApiModelProperty(value = "采购在途数量")
    private BigDecimal purchaseQuantity;
    @ApiModelProperty(value = "在库数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "可用数量")
    private BigDecimal useableQuantity;
    @ApiModelProperty(value = "发货在途数量")
    private BigDecimal shippingQuantity;
    @ApiModelProperty(value = "领料在途")
    private BigDecimal deliverQuantity;
    @ApiModelProperty(value = "领料单锁定量")
    private BigDecimal deliverLockQuantity;
    @ApiModelProperty(value = "在检数量")
    private BigDecimal qualityQuantity;
    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQuantity;
    @ApiModelProperty(value = "仓库类型编码")
    private String wareLocationClassCode;
    @ApiModelProperty(value = "仓库类型名称")
    private String wareLocationClassName;
    @ApiModelProperty(value = "仓库编码：仓库或车间一级")
    private String wareLocationCode;
    @ApiModelProperty(value = "仓库名称")
    private String wareLocationName;

    @ApiModelProperty(value = "批次库存")
    private List<WmsMaterialBatchStocksVO> batchStocks;
}
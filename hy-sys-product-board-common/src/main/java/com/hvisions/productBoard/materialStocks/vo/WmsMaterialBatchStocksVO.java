package com.hvisions.productBoard.materialStocks.vo;

import com.hvisions.productBoard.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel(description = "批次库存表")
@EqualsAndHashCode(callSuper = true)
@Data
public class WmsMaterialBatchStocksVO extends SysBaseDTO {
    @ApiModelProperty(value = "物料类型")
    private String materialTypeCode;
    @ApiModelProperty(value = "物料类型名称")
    private String materialTypeName;
    @ApiModelProperty(value = "物料分组")
    private String materialGroupCode;
    @ApiModelProperty(value = "物料分组名称")
    private String materialGroupName;
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    @ApiModelProperty(value = "物料规格")
    private String materialEigenvalue;
    @ApiModelProperty(value = "图号")
    private String drawingNum;
    @ApiModelProperty(value = "物料简称")
    private String materialDescribe;
    // 原料的实际批次 成品的销售批次
    @ApiModelProperty(value = "物料批次")
    private String materialBatchNum;
    @ApiModelProperty(value = "原材料采购批次")
    private String purchaseBatchNum;
    @ApiModelProperty(value = "成品生产批次")
    private String saleBatch;
    @ApiModelProperty(value = "供应商物料批次")
    private String supplierMaterialBatchNum;
    @ApiModelProperty(value = "入库时间")
    private LocalDateTime shiftDateTime;
    @ApiModelProperty(value = "生产日期")
    private LocalDateTime productDateTime;
    @ApiModelProperty(value = "过期日期")
    private LocalDateTime effectDateTime;
    @ApiModelProperty(value = "过期状态")
    private Integer expireState;
    @ApiModelProperty(value = "质检状态")
    private Integer qualityState;
    @ApiModelProperty(value = "试验状态")
    private Integer testState;
    @ApiModelProperty(value = "物料单位")
    private String materialUnit;
    @ApiModelProperty(value = "物料单位中文")
    private String materialUnitName;
    @ApiModelProperty(value = "在库数量")
    private BigDecimal quantity;
    @ApiModelProperty(value = "可用数量")
    private BigDecimal useableQuantity;
    @ApiModelProperty(value = "发货在途数量")
    private BigDecimal shippingQuantity;
    @ApiModelProperty(value = "在检数量")
    private BigDecimal qualityQuantity;
    @ApiModelProperty(value = "冻结数量")
    private BigDecimal frozenQuantity;
    @ApiModelProperty(value = "领料在途量")
    private BigDecimal deliveryQuantity;
    @ApiModelProperty(value = "领料单锁定量")
    private BigDecimal deliverLockQuantity;
    @ApiModelProperty(value = "仓库编码")
    private String wareLocationCode;
    @ApiModelProperty(value = "仓库名称")
    private String wareLocationName;
    @ApiModelProperty(value = "仓库类型编码")
    private String wareLocationClassCode;
    @ApiModelProperty(value = "仓库类型名称")
    private String wareLocationClassName;
}
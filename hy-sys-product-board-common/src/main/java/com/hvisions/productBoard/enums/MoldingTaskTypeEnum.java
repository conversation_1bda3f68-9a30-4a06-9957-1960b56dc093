package com.hvisions.productBoard.enums;

import lombok.Getter;

@Getter
public enum MoldingTaskTypeEnum {

    MIXING_TASK(0, "配料"),
    COMPACTION_TASK(10, "压坯"),
    EXTRUSION_TASK(20, "推压"),
    SELF_INSPECTION_TASK(30, "车间自检"),
    QUENCHING_TASK(40, "淬火"),
    SIZING_TASK(50, "定型"),
    PRE_SINTERING_TASK(60, "烧结");

    private final Integer value;
    private final String name;

    MoldingTaskTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(Integer value) {
        for (MoldingTaskTypeEnum moldingTaskTypeEnum : MoldingTaskTypeEnum.values()) {
            if (moldingTaskTypeEnum.value.equals(value)) {
                return moldingTaskTypeEnum.name;
            }
        }
        return null;
    }
}

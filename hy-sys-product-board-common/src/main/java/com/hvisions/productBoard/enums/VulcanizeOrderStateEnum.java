package com.hvisions.productBoard.enums;

import lombok.Getter;

@Getter
public enum VulcanizeOrderStateEnum {

    NEW(0, "新建"),
    QUALITY_CHECK(10, "质检中"),
    DELIVERED(20, "已出厂"),
    COMPLETED(30, "已完成"),
    RETURN_FACTORY(40, "返厂待检");

    private final Integer value;
    private final String name;

    VulcanizeOrderStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}

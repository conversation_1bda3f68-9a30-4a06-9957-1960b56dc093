package com.hvisions.productBoard.enums;

import lombok.Getter;

@Getter
public enum MoldingWorkOrderStateEnum {

    NEW(0, "新建"),
    READY(10, "已就绪"),
    PRODUCING(20, "试制中"),
    PRODUCED(30, "生产中"),
    REPRODUCED(40, "重新生产"),
    PRODUCED_FINISH(50, "生产完成"),
    SUBMIT(60, "已提交");

    private final Integer value;
    private final String name;

    MoldingWorkOrderStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}

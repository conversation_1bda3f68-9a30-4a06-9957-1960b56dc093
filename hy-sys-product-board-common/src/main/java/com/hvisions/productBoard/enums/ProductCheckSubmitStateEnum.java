package com.hvisions.productBoard.enums;

import lombok.Getter;

@Getter
public enum ProductCheckSubmitStateEnum {

    PENDING_QUALITY(0, "待质检"),
    PENDING_MILITARY(30, "待军检"),
    PENDING_PRINT(35, "待打印合格证"),
    PENDING_PACKAGED(40, "待包装"),
    PACKAGED(50, "已包装"),
    DISCARD(-100, "废弃");

    private final Integer value;
    private final String name;

    ProductCheckSubmitStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}

package com.hvisions.productBoard.enums;

import lombok.Getter;

@Getter
public enum EnhanceTaskTypeEnum {

    MATERIAL_PICKING(0, "领料辅助"),
    PREFORMING(1, "预成型"),
    STRANDING(2, "合股"),
    WINDING(3, "缠绕"),
    BRAIDING(4, "编织"),
    DEMOLDING(5, "脱芯");

    private final Integer value;
    private final String name;

    EnhanceTaskTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}

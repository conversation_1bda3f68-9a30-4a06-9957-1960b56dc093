package com.hvisions.productBoard.enums;

import lombok.Getter;

@Getter
public enum HoseInspectionStateEnum {

    NEW(0, "新建"),
    PENDING_CHECK(5, "待复核"),
    COMPLETED(10, "试验完成"),
    PENDING_QA(15, "待质检"),
    PENDING_TRAIL(20, "待审理"),
    PASSED(25, "质检完成"),
    CLOSED(50, "已关闭"),
    PENDING_CONFIRMED(100, "待确认"),
    DISCARD(-100, "废弃");

    private final Integer value;
    private final String name;

    HoseInspectionStateEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}

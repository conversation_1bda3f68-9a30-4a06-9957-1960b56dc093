package com.hvisions.productBoard.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@ApiModel(description = "五部总装首页-工位产出记录")
@Data
public class PmAssemblyStepOutputResp {

    @ApiModelProperty(value = "工位名称")
    @JsonProperty(value = "xAxisData")
    private List<String> xAxisData;

    @ApiModelProperty(value = "产品根数")
    private Map<String, BigDecimal> quantityMap;

}

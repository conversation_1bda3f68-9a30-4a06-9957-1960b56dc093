package com.hvisions.productBoard.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@ApiModel(description = "总装看板-近7天完成数量统计")
@Data
public class BiAssemblyStepFinishedQuantityStatisticResp {

    @ApiModelProperty(value = "日期")
    @JsonProperty(value = "xAxisData")
    private List<String> xAxisData;

    @ApiModelProperty(value = "完成数")
    private Map<String, BigDecimal> quantityMap;

}

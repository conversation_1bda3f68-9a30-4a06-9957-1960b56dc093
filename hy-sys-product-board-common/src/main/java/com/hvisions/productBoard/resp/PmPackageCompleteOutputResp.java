package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@Builder
@ApiModel(description = "五部办公室看板-包装-完成统计")
@EqualsAndHashCode(callSuper = true)
public class PmPackageCompleteOutputResp extends CompleteResp {

    // 根数
    private BigDecimal output;

    // 订单数
    private BigDecimal orderNum;

    // 日期
    private String dateStr;

    // 维度
    private String period;
}

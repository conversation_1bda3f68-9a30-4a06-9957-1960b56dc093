package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "五部总装首页-数据概览")
@Data
public class PmAssemblyOverviewResp {

    @ApiModelProperty(value = "进行中任务")
    private Long ingOrderCount;

    @ApiModelProperty(value = "新增任务")
    private Long newOrderCount;

    @ApiModelProperty(value = "瓶颈订单数量")
    private Long bottleneckOrderCount;

    @ApiModelProperty(value = "缺少零件数量")
    private BigDecimal lackPartsQuantity;

    @ApiModelProperty(value = "可用零件数量")
    private BigDecimal availablePartsQuantity;

}

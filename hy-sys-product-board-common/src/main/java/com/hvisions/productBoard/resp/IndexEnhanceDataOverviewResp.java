package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
public class IndexEnhanceDataOverviewResp {
    @ApiModelProperty(value = "进行中任务")
    private Integer ongoingOrder;
    @ApiModelProperty(value = "新增任务")
    private Integer newOrder;
    @ApiModelProperty(value = "临期订单")
    private Integer nearOrder;
    @ApiModelProperty(value = "今日完成订单")
    private Integer todayFinishOrder;
    @ApiModelProperty(value = "今日产量")
    private BigDecimal todayOutput;
}

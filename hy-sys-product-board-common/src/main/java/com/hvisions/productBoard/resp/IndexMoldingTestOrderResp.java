package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
public class IndexMoldingTestOrderResp {
    @ApiModelProperty(value = "生产任务单号")
    private String orderCode;
    @ApiModelProperty(value = "产品名称")
    private String productName;
    @ApiModelProperty(value = "规格型号")
    private String productEigenvalue;
    @ApiModelProperty(value = "订单数量")
    private Integer orderQuantity;
    @ApiModelProperty(value = "产品批次")
    private String batchNum;
    @ApiModelProperty(value = "生产进度")
    private Double percentage;
    @ApiModelProperty(value = "生产数量")
    private BigDecimal productionQuantity;
}

package com.hvisions.productBoard.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@ApiModel(value = "生产首页-五部办公室-总装计划达成率")
@Data
public class PmOfficeAssemblyPlanQuantityAndPercentResp {

    @ApiModelProperty(value = "天名称/周名称/月名称")
    @JsonProperty(value = "xAxisData")
    private List<String> xAxisData;

    @ApiModelProperty(value = "每天完成量")
    private Map<String, BigDecimal> quantityMap;

    @ApiModelProperty(value = "每天完成百分比")
    private Map<String, BigDecimal> percentMap;

}

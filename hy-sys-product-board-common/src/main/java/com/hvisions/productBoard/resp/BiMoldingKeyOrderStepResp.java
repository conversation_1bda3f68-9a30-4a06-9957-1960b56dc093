package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
public class BiMoldingKeyOrderStepResp {
    @ApiModelProperty(value = "工序名称")
    private String stepName;
    @ApiModelProperty(value = "完成数量")
    private BigDecimal finishedQuantity;
    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQuantity;
    @ApiModelProperty(value = "完成率")
    private Double percentage;
    @ApiModelProperty(value = "今天完成数量")
    private BigDecimal todayFinishedQuantity;
    @ApiModelProperty(value = "今天计划数量")
    private BigDecimal todayPlanQuantity;
    @ApiModelProperty(value = "单位")
    private String unit;
}

package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@ApiModel(description = "总装车间看板-工位任务生产情况")
@Accessors(chain = true)
@Data
public class BiAssemblyStepFinishedQuantityLastAndCurrWeekResp {

    @ApiModelProperty(value = "工位名称")
    private String stepName;

    @ApiModelProperty(value = "工位完成数量-上周")
    private BigDecimal finishedQuantityOfLastWeek;

    @ApiModelProperty(value = "工位完成数量-本周")
    private BigDecimal finishedQuantityOfCurrWeek;

}

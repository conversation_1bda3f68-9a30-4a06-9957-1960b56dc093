package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
public class BiPackageCompleteOutputResp {
    @ApiModelProperty("订单数")
    private Integer orderCount;
    @ApiModelProperty("根数")
    private BigDecimal quantity;
    @ApiModelProperty("日期")
    private String dateStr;
}

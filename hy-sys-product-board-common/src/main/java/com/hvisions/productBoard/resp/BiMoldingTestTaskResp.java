package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
public class BiMoldingTestTaskResp {

    @ApiModelProperty(value = "任务单号")
    private String code;

    @ApiModelProperty(value = "型号规格")
    private String eigenvalue;

    @ApiModelProperty(value = "产品批次")
    private String batchNo;

    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;

    @ApiModelProperty(value = "当前工序")
    private String stepName;
}

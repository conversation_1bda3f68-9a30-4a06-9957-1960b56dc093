package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
public class BiEnhanceDataOverviewResp {

    @ApiModelProperty(value = "订单数-已完成")
    private Integer finishedOrderCount;

    @ApiModelProperty(value = "订单数-总数")
    private Integer totalOrderCount;

    @ApiModelProperty(value = "产品量-已完成")
    private BigDecimal finishedOrderQuantity;

    @ApiModelProperty(value = "产品量-总数")
    private BigDecimal totalOrderQuantity;
}

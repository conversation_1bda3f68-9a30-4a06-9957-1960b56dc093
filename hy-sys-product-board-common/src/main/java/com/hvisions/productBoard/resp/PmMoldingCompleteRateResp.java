package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@Builder
@ApiModel(description = "五部办公室看板-成型-计划达成率")
@EqualsAndHashCode(callSuper = true)
public class PmMoldingCompleteRateResp extends CompleteResp {

    // 产量（米）
    private BigDecimal output;

    // 计划达成率
    private BigDecimal rate;

    // 日期
    private String dateStr;

    // 维度
    private String period;
}

package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "总装工位关键任务")
@Data
public class BiAssemblyStepKeyOrderResp {

    @ApiModelProperty(value = "原始记录单号")
    private String code;

    @ApiModelProperty(value = "生产任务单号")
    private String workOrderCode;

    @ApiModelProperty(value = "备货需求单合同号")
    private String saleOrderContractCode;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "产品规格")
    private String productEigenvalue;

    @ApiModelProperty(value = "任务数量")
    private BigDecimal planQuantity;

    @ApiModelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty(value = "原料准备")
    private BigDecimal rawMaterialPercent;

}

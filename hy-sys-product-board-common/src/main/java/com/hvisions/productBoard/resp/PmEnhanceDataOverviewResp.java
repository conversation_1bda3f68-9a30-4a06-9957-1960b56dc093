package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@ApiModel(description = "五部办公室看板-增强-订单概览")
public class PmEnhanceDataOverviewResp {

    @ApiModelProperty(value = "订单总数")
    private Integer orderCount;

    @ApiModelProperty(value = "订单总米数")
    private BigDecimal orderQuantity;

    @ApiModelProperty(value = "临期订单总数")
    private Integer nearOrderCount;

    @ApiModelProperty(value = "临期订单总米数")
    private BigDecimal nearOrderQuantity;

    @ApiModelProperty(value = "异常订单总数")
    private Integer errorOrderCount;

    @ApiModelProperty(value = "异常订单总米数")
    private BigDecimal errorOrderQuantity;
}

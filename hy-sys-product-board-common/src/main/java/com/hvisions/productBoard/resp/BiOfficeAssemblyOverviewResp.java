package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "五部办公室看板-总装-订单概览")
@Data
public class BiOfficeAssemblyOverviewResp {

    @ApiModelProperty(value = "订单总数")
    private Long orderCount;

    @ApiModelProperty(value = "订单总根数")
    private BigDecimal orderQuantity;

    @ApiModelProperty(value = "临期订单总数")
    private Long nearOrderCount;

    @ApiModelProperty(value = "临期订单总根数")
    private BigDecimal nearOrderQuantity;

    @ApiModelProperty(value = "异常订单总数")
    private Long errorOrderCount;

    @ApiModelProperty(value = "异常订单总根数")
    private BigDecimal errorOrderQuantity;

}

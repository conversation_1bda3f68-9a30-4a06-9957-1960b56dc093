package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class BiInspectionUnqualifiedDistributionResp {

    @ApiModelProperty(value = "不合格类型")
    private String type;
    @ApiModelProperty(value = "数量")
    private Integer quantity;
    @ApiModelProperty(value = "占比")
    private Double percentage;
}

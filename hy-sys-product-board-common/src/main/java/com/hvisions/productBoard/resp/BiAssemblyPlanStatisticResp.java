package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "五部总装看板-本月需完成计划")
@Data
public class BiAssemblyPlanStatisticResp {

    @ApiModelProperty(value = "订单数-已完成")
    private Long finishedOrderCount;

    @ApiModelProperty(value = "订单数-总数")
    private Long totalOrderCount;

    @ApiModelProperty(value = "产品量-已完成")
    private BigDecimal finishedOrderQuantity;

    @ApiModelProperty(value = "产品量-总数")
    private BigDecimal totalOrderQuantity;
}

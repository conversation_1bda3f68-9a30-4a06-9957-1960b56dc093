package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class BiInspectionKeyOrderResp {

    @ApiModelProperty(value = "软管组件检验单号")
    private String code;
    @ApiModelProperty(value = "已完成检验数量")
    private Integer finishQuantity;
    @ApiModelProperty(value = "总检验数量")
    private Integer totalQuantity;
    @ApiModelProperty(value = "检验进度")
    private Double percentage;
    @ApiModelProperty(value = "已完成军检数量")
    private Integer mFinishQuantity;
    @ApiModelProperty(value = "总军检数量")
    private Integer mTotalQuantity;
    @ApiModelProperty(value = "军检进度")
    private Double mPercentage;
    @ApiModelProperty(value = "是否紧急订单")
    private Boolean isUrgency;
}

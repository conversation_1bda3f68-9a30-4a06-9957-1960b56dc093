package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class BiPackageOnDutyResp {
    @ApiModelProperty(value = "工号")
    private String code;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "照片")
    private String photo;
    @ApiModelProperty(value = "状态")
    private String status;
}

package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel(description = "关键订单生产状态实时监控")
@Data
public class PmAssemblyKeyOrderResp {

    @ApiModelProperty(value = "任务单编码")
    private String code;

    @ApiModelProperty(value = "是否紧急")
    private Boolean isUrgency;

    @ApiModelProperty(value = "是否试验任务单")
    private Boolean isTest;

    @ApiModelProperty(value = "是否销售订单")
    private Boolean isSaleOrder;

    @ApiModelProperty(value = "是否科研任务单")
    private Boolean isResearchOrder;

    @ApiModelProperty(value = "总装生产计划明细ID")
    private Integer workPlanLineId;

    /**
     * 销售订单->生产计划->生产任务单  生产
     * 科研任务单->生产计划->生产任务单 科研
     * 增强批次余额 -> 试验任务单(正常流程)  试验
     * 增强批次余额 -> 试验任务单(非正常流程) 试验
     */

    @ApiModelProperty(value = "工单类型")
    private Integer type;

    @ApiModelProperty(value = "工单类型")
    private String typeName;

    @ApiModelProperty(value = "用途")
    private Integer useWay;

    @ApiModelProperty(value = "用途")
    private String useWayName;

    @ApiModelProperty(value = "生成模式")
    private String productionMode;

    @ApiModelProperty(value = "生产类型")
    private Integer productionType;

    @ApiModelProperty(value = "生产类型")
    private String productionTypeName;

    @ApiModelProperty(value = "产品类型")
    private Integer productType;

    @ApiModelProperty(value = "产品类型")
    private String productTypeName;

    @ApiModelProperty(value = "军种")
    private String armyType;

    @ApiModelProperty(value = "机型")
    private String machineType;

    @ApiModelProperty(value = "交付单位")
    private String customCompany;

    @ApiModelProperty(value = "销售订单")
    private String saleOrderCode;

    @ApiModelProperty(value = "销售订单明细ID")
    private Integer saleOrderLineId;

    @ApiModelProperty(value = "销售订单合同号")
    private String saleOrderContractCode;

    @ApiModelProperty(value = "科研任务单号")
    private String researchOrderCode;

    @ApiModelProperty(value = "科研任务订单明细ID")
    private Integer researchOrderLineId;

    @ApiModelProperty(value = "交付单位")
    private Integer customerId;

    @ApiModelProperty(value = "交付单位")
    private String customerName;

    @ApiModelProperty(value = "物料ID")
    private Integer materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "规格")
    private String eigenvalue;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "物料单位")
    private String materialUnitName;

    @ApiModelProperty(value = "物料单位")
    private String materialUnitSymbol;

    @ApiModelProperty(value = "图号")
    private String drawingNo;

    @ApiModelProperty(value = "批次")
    private String materialBatchNum;

    @ApiModelProperty(value = "实际批次")
    private String actualMaterialBatchNum;

    @ApiModelProperty(value = "BOM ID")
    private Integer bomId;

    @ApiModelProperty(value = "BOM 编码")
    private String bomCode;

    @ApiModelProperty(value = "BOM 名称")
    private String bomName;

    @ApiModelProperty(value = "BOM 版本")
    private String bomVersion;

    @ApiModelProperty(value = "工艺路线ID")
    private Integer materialRouteId;

    @ApiModelProperty(value = "工艺路线编码")
    private String materialRouteCode;

    @ApiModelProperty(value = "工艺路线名称")
    private String materialRouteName;

    @ApiModelProperty(value = "工艺路线备注")
    private String materialRouteRemark;

    @ApiModelProperty(value = "图纸版本")
    private String drawingVersion;

    @ApiModelProperty(value = "图纸版本")
    private String drawingVersionLatest;

    @ApiModelProperty(value = "图纸状态")
    private Integer drawingState;

    @ApiModelProperty(value = "图纸更新时间")
    private Boolean isDrawingNeedUpdate;

    @ApiModelProperty(value = "图纸更新时间")
    private LocalDateTime drawingUpdateDateTime;

    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStartTime;

    @ApiModelProperty(value = "计划结束时间")
    private LocalDateTime planEndTime;

    @ApiModelProperty(value = "实际开始时间")
    private LocalDateTime actualStartTime;

    @ApiModelProperty(value = "实际结束时间")
    private LocalDateTime actualEndTime;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQuantity;

    @ApiModelProperty(value = "下发原始记录单数量汇总")
    private BigDecimal subQuantity;

    @ApiModelProperty(value = "实际产出数量")
    private BigDecimal productQuantity;

    @ApiModelProperty(value = "合格数量")
    private BigDecimal qualifiedQuantity;

    @ApiModelProperty(value = "不合格数量")
    private BigDecimal unqualifiedQuantity;

    @ApiModelProperty(value = "生产状态")
    private Integer state;

    @ApiModelProperty(value = "领料状态")
    private Integer deliverState;

    @ApiModelProperty(value = "领料单号")
    private String deliverOrderCode;

    @ApiModelProperty(value = "交货日期")
    private LocalDateTime deliveryDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "图纸软管长度")
    private String drawingHoseLength;

    @ApiModelProperty(value = "工作压力")
    private String workingPressure;

    @ApiModelProperty(value = "耐压压力")
    private String withstandPressure;

    @ApiModelProperty(value = "收缩套压力")
    private String shrink;

    @ApiModelProperty(value = "是否需要二维码")
    private String qrcode;

    @ApiModelProperty(value = "组件段数")
    private Integer multiNum;

    @ApiModelProperty(value = "车间ID")
    private Integer areaId;

    @ApiModelProperty(value = "产线ID")
    private Integer cellId;

    // ! ---------------------------------------------------------------------------------------------------------------

    @ApiModelProperty(value = "原料领料数量")
    private BigDecimal rawMaterialDeliveryQuantity;

    @ApiModelProperty(value = "原料BOM需求数量")
    private BigDecimal rawMaterialBomRequireQuantity;

    @ApiModelProperty(value = "原料准备")
    private BigDecimal rawMaterialPercent;

    @ApiModelProperty(value = "生产执行")
    private BigDecimal productionPercent;
}

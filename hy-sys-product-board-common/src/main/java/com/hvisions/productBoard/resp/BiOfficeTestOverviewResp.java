package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "生产综合看板-五部办公室看板-试验-订单概览")
@Data
public class BiOfficeTestOverviewResp {

    @ApiModelProperty(value = "订单总数 = 试验订单总数 + 耐压订单总数")
    private Long orderCount;

    @ApiModelProperty(value = "订单总根数 = 试验订单总根数 + 耐压订单总根数")
    private BigDecimal orderQuantity;

    @ApiModelProperty(value = "试验订单总数")
    private Long testOrderCount;

    @ApiModelProperty(value = "试验订单总根数")
    private BigDecimal testOrderQuantity;

    @ApiModelProperty(value = "耐压订单总数")
    private Long pressureOrderCount;

    @ApiModelProperty(value = "耐压订单总根数")
    private BigDecimal pressureOrderQuantity;

}

package com.hvisions.productBoard.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@ApiModel(value = "生产综合看板-五部试验中心-耐压完成统计")
@Data
public class BiTestFinishedQuantityStatisticResp {

    @ApiModelProperty(value = "天名称/周名称/月名称")
    @JsonProperty(value = "xAxisData")
    private List<String> xAxisData;

    @ApiModelProperty(value = "订单数")
    private Map<String, Long> countMap;

    @ApiModelProperty(value = "根数")
    private Map<String, BigDecimal> quantityMap;

}

package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class BiInspectionCompleteOutputResp {

    @ApiModelProperty("订单数")
    private Integer orderCount;
    @ApiModelProperty("产品数（根）")
    private Integer productCount;
    @ApiModelProperty("日期")
    private String date;
}

package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@ApiModel(description = "关键订单生产状态实时监控")
@Accessors(chain = true)
@Data
public class BiAssemblyKeyOrderResp {

    @ApiModelProperty(value = "生产任务单号")
    private String workOrderCode;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "原料准备")
    private BigDecimal rawMaterialPercent;

    @ApiModelProperty(value = "生产执行")
    private BigDecimal productionPercent;

}

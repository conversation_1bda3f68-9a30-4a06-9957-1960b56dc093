package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class BiMoldingKeyOrderResp {
    @ApiModelProperty(value = "订单编码")
    private String orderCode;
    @ApiModelProperty(value = "产品类型")
    private String productType;
    @ApiModelProperty(value = "工序")
    private List<BiMoldingKeyOrderStepResp> steps;
}

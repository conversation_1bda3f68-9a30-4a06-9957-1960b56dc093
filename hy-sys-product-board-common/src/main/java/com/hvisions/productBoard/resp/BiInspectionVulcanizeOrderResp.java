package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class BiInspectionVulcanizeOrderResp {

    @ApiModelProperty(value = "生产任务单号")
    private String code;
    @ApiModelProperty(value = "产品数量")
    private Integer quantity;
    @ApiModelProperty(value = "检验进度")
    private String state;
}

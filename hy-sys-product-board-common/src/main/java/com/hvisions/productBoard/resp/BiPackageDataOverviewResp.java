package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
public class BiPackageDataOverviewResp {
    @ApiModelProperty(value = "订单数")
    private Integer taskCount;
    @ApiModelProperty(value = "产品数（根）")
    private BigDecimal taskQuantity;
}

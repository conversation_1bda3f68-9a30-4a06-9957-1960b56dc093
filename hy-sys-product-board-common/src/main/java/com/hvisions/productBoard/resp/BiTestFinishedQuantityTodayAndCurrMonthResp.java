package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(description = "生产综合看板-五部试验中心-耐压任务完成情况")
@Data
public class BiTestFinishedQuantityTodayAndCurrMonthResp {

    @ApiModelProperty(value = "今日完成订单数")
    private Long todayFinishedCount;

    @ApiModelProperty(value = "今日完成根数")
    private BigDecimal todayFinishedQuantity;

    @ApiModelProperty(value = "本月完成订单数")
    private Long monthFinishedCount;

    @ApiModelProperty(value = "本月完成根数")
    private BigDecimal monthFinishedQuantity;

}

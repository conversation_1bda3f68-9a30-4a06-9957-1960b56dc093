package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class BiPackageOrderResp {
    @ApiModelProperty(value = "合同号")
    private String contractCode;
    @ApiModelProperty(value = "提交单号")
    private String submitOrderCode;
    @ApiModelProperty(value = "产品型号")
    private String productModel;
    @ApiModelProperty(value = "产品规格")
    private String productSpec;
    @ApiModelProperty(value = "包装数量")
    private Integer quantity;
}

package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class BiInspectionDataOverviewResp {

    @ApiModelProperty(value = "待检任务")
    private Integer taskCount;
    @ApiModelProperty(value = "待检数量（根）")
    private Integer taskQuantity;
    @ApiModelProperty(value = "本月新增任务")
    private Integer newTaskCount;
    @ApiModelProperty(value = "新增数量（根）")
    private Integer newTaskQuantity;
}

package com.hvisions.productBoard.vo;

import com.hvisions.productBoard.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

@ApiModel(description = "备货需求单")
@EqualsAndHashCode(callSuper = true)
@Data
public class SaSaleOrderVO extends SysBaseDTO {

    @ApiModelProperty(value = "制单人")
    private String creatorName;

    @ApiModelProperty(value = "父备货需求号")
    private String parentCode;

    @ApiModelProperty(value = "备货需求号")
    private String code;

    @ApiModelProperty(value = "对方合同号")
    private String contractCode;

    @ApiModelProperty(value = "购货单位")
    private Integer customerId;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户Sap编码")
    private String customerSapCode;

    @ApiModelProperty(value = "客户简称")
    private String customerName;

    @ApiModelProperty(value = "客户全称")
    private String customerFullName;

    @ApiModelProperty(value = "购货单位名称")
    private String customerCompany;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "军种")
    private String armyType;

    @ApiModelProperty(value = "机型")
    private String machineType;

    @ApiModelProperty(value = "预计完成日期")
    private LocalDate planFinishDate;

    @ApiModelProperty(value = "警告日期")
    private LocalDate warningDate;

    @ApiModelProperty(value = "异常日期")
    private LocalDate exceptionDate;

    @ApiModelProperty(value = "关闭状态")
    private Integer closeState;

    @ApiModelProperty(value = "执行状态")
    private Integer executeState;

    @ApiModelProperty(value = "合同状态")
    private Integer contractState;

    @ApiModelProperty(value = "下派数量")
    private BigDecimal dispatchCount;

    @ApiModelProperty(value = "下派状态")
    private Integer dispatchState;

    @ApiModelProperty(value = "计划总量")
    private BigDecimal planCount;

    @ApiModelProperty(value = "生产完成总量")
    private BigDecimal productionCount;

    @ApiModelProperty(value = "质检完成总量")
    private BigDecimal qualityCount;

    @ApiModelProperty(value = "军检完成总量")
    private BigDecimal militaryQualityCount;

    @ApiModelProperty(value = "入库完成总量")
    private BigDecimal wareCount;

    @ApiModelProperty(value = "发货完成总量")
    private BigDecimal deliverCount;

    @ApiModelProperty(value = "开票完成总量")
    private BigDecimal invoiceCount;

    @ApiModelProperty(value = "生产取消总量")
    private BigDecimal productionCancelCount;

    @ApiModelProperty(value = "运输方式")
    private String transport;

    @ApiModelProperty(value = "费用负担")
    private String freight;

    @ApiModelProperty(value = "发货状态")
    private Integer shippingState;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "预计发货数量")
    private BigDecimal predictShippingCount;

    @ApiModelProperty(value = "可发货数量")
    private BigDecimal shipmentCount;

    @ApiModelProperty(value = "物料分组类型（三部）")
    private String materialGroupTypeCode;

    @ApiModelProperty(value = "物料分组类型（三部）")
    private String materialGroupTypeName;

    @ApiModelProperty(value = "客户验收")
    private Boolean isCustomerQualify;

    @ApiModelProperty(value = "待检量")
    private BigDecimal waitQualityCount;

    @ApiModelProperty(value = "待入库量")
    private BigDecimal waitWareCount;

    @ApiModelProperty(value = "用途")
    private Integer purpose;

}

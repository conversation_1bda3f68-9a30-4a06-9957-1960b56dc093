package com.hvisions.productBoard.vo;

import com.hvisions.productBoard.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

@ApiModel(description = "")
@EqualsAndHashCode(callSuper = true)
@Data
public class SaSaleOrderLineVO extends SysBaseDTO {

    @ApiModelProperty(value = "创建日期")
    private LocalDate createDate;

    @ApiModelProperty(value = "制单人")
    private String creatorName;

    @ApiModelProperty(value = "备货需求号")
    private String saleOrderCode;

    @ApiModelProperty(value = "对方合同号")
    private String contractCode;

    @ApiModelProperty(value = "军种")
    private String armyType;

    @ApiModelProperty(value = "机型")
    private String machineType;

    @ApiModelProperty(value = "客户")
    private Integer customerId;

    @ApiModelProperty(value = "客户简称")
    private String customerName;

    @ApiModelProperty(value = "客户全称")
    private String customerFullName;

    @ApiModelProperty(value = "购货单位")
    private String customerCompany;

    @ApiModelProperty(value = "产品ID")
    private Integer productId;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "产品规格")
    private String productEigenvalue;

    @ApiModelProperty(value = "图号")
    private String drawingNo;

    @ApiModelProperty(value = "单位id")
    private Integer productUom;

    @ApiModelProperty(value = "单位名称")
    private String productUomName;

    @ApiModelProperty(value = "单位符号")
    private String productUomDesc;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal unitPriceTax;

    @ApiModelProperty(value = "价税合计")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "交货日期")
    private LocalDate deliverDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "下发数量")
    private BigDecimal dispatchCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "生产完成数量")
    private BigDecimal productionCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "生产取消数量")
    private BigDecimal productionCancelCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "质检完成数量")
    private BigDecimal qualityCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "军检完成数量")
    private BigDecimal militaryQualityCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "入库完成数量")
    private BigDecimal wareCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "发货完成数量")
    private BigDecimal deliverCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "开票完成数量")
    private BigDecimal invoiceCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "关闭状态")
    private Integer closeState;

    @ApiModelProperty(value = "下派状态")
    private Integer dispatchState;

    @ApiModelProperty(value = "发货状态")
    private Integer shippingState;

    @ApiModelProperty(value = "预计发货数量")
    private BigDecimal predictShippingCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "可发货数量")
    private BigDecimal shipmentCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "部门")
    private String department;

    @ApiModelProperty(value = "物料分组")
    private String materialGroupCode;

    @ApiModelProperty(value = "物料分组")
    private String materialGroupName;

    @ApiModelProperty(value = "物料分组类型")
    private String materialGroupTypeCode;

    @ApiModelProperty(value = "物料分组类型")
    private String materialGroupTypeName;

    @ApiModelProperty(value = "质检策略：-1: 车间检验，-2: 自检，-3: 免检，其他数值: 质检标准")
    private Integer qualityStrategy;

    @ApiModelProperty(value = "质检策略/质检标准名称")
    private String qualityStrategyName;

    @ApiModelProperty(value = "半成品物料编码")
    private String semiMaterialCode;

    @ApiModelProperty(value = "半成品质检标准ID")
    private Integer semiQualityStandardId;

    @ApiModelProperty(value = "半成品质检标准编码")
    private String semiQualityStandardCode;

    @ApiModelProperty(value = "半成品质检标准名称")
    private String semiQualityStandardName;

    @ApiModelProperty(value = "半成品质检标准版本")
    private String semiQualityStandardVersion;

    @ApiModelProperty(value = "待检量")
    private BigDecimal waitQualityCount = BigDecimal.ZERO;

    @ApiModelProperty(value = "待入库量")
    private BigDecimal waitWareCount = BigDecimal.ZERO;

}

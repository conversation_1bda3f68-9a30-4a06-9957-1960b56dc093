package com.hvisions.productBoard.req;

import com.hvisions.common.dto.PageInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 包装订单分页请求对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BiPackageOrderReq extends PageInfo {
    
    /**
     * 订单编码列表
     */
    private List<String> codes;
    
    /**
     * 创建包装订单请求对象
     * @param current 当前页码
     * @param size 每页大小
     * @param codes 订单编码列表
     * @return BiPackageOrderReq
     */
    public static BiPackageOrderReq of(Integer current, Integer size, List<String> codes) {
        BiPackageOrderReq req = new BiPackageOrderReq();
        req.setPageNum(current);
        req.setPageSize(size);
        req.setCodes(codes);
        return req;
    }
}

package com.hvisions.productBoard.req;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(description = "原始记录单查询")
@EqualsAndHashCode(callSuper = true)
@Data
public class PmAssemblyWorkSubOrderPageQueryReq extends PageInfo {

    @ApiModelProperty(value = "工位名称")
    private String operationName;

    @ApiModelProperty(value = "备货需求单")
    private String saleOrderCode;

    @ApiModelProperty(value = "生产任务单号")
    private String workOrderCode;

    @ApiModelProperty(value = "原始记录单号")
    private String workSubOrderCode;

    @ApiModelProperty(value = "产品编码")
    private String materialCode;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "规格")
    private String eigenvalue;

    @ApiModelProperty(value = "是否加急")
    private Boolean isUrgent;

}

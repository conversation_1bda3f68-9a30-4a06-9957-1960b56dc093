package com.hvisions.productBoard.req;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "关键订单生产状态实时监控查询")
@Data
public class PmAssemblyKeyOrderReq extends PageInfo {

    @ApiModelProperty(value = "关键字: 生产任务单、备货需求单、产品型号")
    private String keyword;

}

package com.hvisions.productBoard.req;

import com.hvisions.common.dto.PageInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用分页请求对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CommonPageReq extends PageInfo {
    
    /**
     * 创建分页请求对象
     * @param current 当前页码
     * @param size 每页大小
     * @return CommonPageReq
     */
    public static CommonPageReq of(Integer current, Integer size) {
        CommonPageReq req = new CommonPageReq();
        req.setPageNum(current);
        req.setPageSize(size);
        return req;
    }
}

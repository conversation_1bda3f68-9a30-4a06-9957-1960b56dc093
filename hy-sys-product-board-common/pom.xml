<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hvisions</groupId>
        <artifactId>hy-sys-product-board</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>hy-sys-product-board-common</artifactId>

    <dependencies>
        <!-- <dependency> -->
        <!--     <groupId>org.projectlombok</groupId> -->
        <!--     <artifactId>lombok</artifactId> -->
        <!-- </dependency> -->
        <!-- <dependency> -->
        <!--     <groupId>io.swagger</groupId> -->
        <!--     <artifactId>swagger-annotations</artifactId> -->
        <!--     <version>1.5.9</version> -->
        <!-- </dependency> -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>framework-common</artifactId>
            <version>1.4.0</version>
        </dependency>

        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>hy-sys-hiper-base-common-v2</artifactId>
            <version>1.2.1-SNAPSHOT</version>
        </dependency>

        <!-- <dependency> -->
        <!--     <groupId>org.springframework.boot</groupId> -->
        <!--     <artifactId>spring-boot-starter-validation</artifactId> -->
        <!-- </dependency> -->
        <!-- <dependency> -->
        <!--     <groupId>com.fasterxml.jackson.core</groupId> -->
        <!--     <artifactId>jackson-annotations</artifactId> -->
        <!--     <version>2.9.0</version> -->
        <!-- </dependency> -->
        <!-- <dependency> -->
        <!--     <groupId>com.fasterxml.jackson.datatype</groupId> -->
        <!--     <artifactId>jackson-datatype-jsr310</artifactId> -->
        <!--     <version>2.9.9</version> -->
        <!-- </dependency> -->

    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>
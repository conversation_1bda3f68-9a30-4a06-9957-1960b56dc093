package com.hvisions.pruductBoard.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.productBoard.materialStocks.vo.WmsMaterialStocksVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;


@FeignClient(value = "product-board",path = "/wms-material-stcks")
public interface CardStockClient {

    @ApiOperation(value = "获取全部库存数据")
    @GetMapping("/all")
    ResultVO<List<WmsMaterialStocksVO>> all();
}
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>hy-sys-product-board</artifactId>
        <groupId>com.hvisions</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>hy-sys-product-board-client</artifactId>
    <dependencies>
        <!-- MyBatis-Plus -->
        <!-- <dependency> -->
        <!--     <groupId>org.springframework.cloud</groupId> -->
        <!--     <artifactId>spring-cloud-starter-feign</artifactId> -->
        <!--     <version>1.4.5.RELEASE</version> -->
        <!-- </dependency> -->
        <!-- <dependency> -->
        <!--     <groupId>org.springframework</groupId> -->
        <!--     <artifactId>spring-web</artifactId> -->
        <!-- </dependency> -->
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>hy-sys-product-board-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>